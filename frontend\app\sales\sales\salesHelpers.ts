import { Sale } from './salesApi';

// Date formatting utility
export const formatDate = (dateString: string | Date) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Status badge color utility
export const getStatusBadgeColor = (status: string) => {
  switch (status) {
    case 'paid':
      return 'bg-green-100 text-green-800';
    case 'partially_paid':
      return 'bg-yellow-100 text-yellow-800';
    case 'unpaid':
      return 'bg-red-100 text-red-800';
    case 'cancelled':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// URL parameter helpers
export const updateUrlParams = (params: Record<string, string>) => {
  
  try {
    const newUrl = new URL(window.location.href);
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        newUrl.searchParams.set(key, value);
      } else {
        newUrl.searchParams.delete(key);
      }
    });
    
    const newUrlString = newUrl.toString();
    
    window.history.pushState({}, '', newUrlString);
    
    // Dispatch a custom event to notify components of URL change
    window.dispatchEvent(new CustomEvent('urlParamsChanged', { detail: params }));
  } catch (error) {
    throw error;
  }
};

 