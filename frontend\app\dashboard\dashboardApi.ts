import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api';

// Dashboard statistics interface
export interface DashboardStats {
  products: number;
  warehouses: number;
  securityEvents: any[];
  userRole?: string;
  warehouseName?: string;
}





// Dashboard API service
export class DashboardApi {
  // Get products count
  static async getProductsCount(): Promise<number> {
    try {
      console.log('[DashboardApi] Fetching products count from:', `${API_BASE}/products/count`);
      const headers = getAxiosAuthHeaders();
      console.log('[DashboardApi] Auth headers:', headers);
      
      const response = await axios.get(`${API_BASE}/products/count`, {
        headers,
      });
      console.log('[DashboardApi] Products count response:', response.data);
      return response.data.count || 0;
    } catch (error: any) {
      console.error('[DashboardApi] Failed to fetch products count:', error);
      if (error.response) {
        console.error('[DashboardApi] Response status:', error.response.status);
        console.error('[DashboardApi] Response data:', error.response.data);
      }
      return 0;
    }
  }

  // Get warehouses count
  static async getWarehousesCount(userUuid?: string): Promise<number> {
    try {
      const url = userUuid 
        ? `${API_BASE}/warehouses/count?userUuid=${encodeURIComponent(userUuid)}`
        : `${API_BASE}/warehouses/count`;
      console.log('[DashboardApi] Fetching warehouses count from:', url);
      const headers = getAxiosAuthHeaders();
      console.log('[DashboardApi] Auth headers for warehouses:', headers);
      
      const response = await axios.get(url, {
        headers,
      });
      console.log('[DashboardApi] Warehouses count response:', response.data);
      return response.data.count || 0;
    } catch (error: any) {
      console.error('[DashboardApi] Failed to fetch warehouses count:', error);
      if (error.response) {
        console.error('[DashboardApi] Response status:', error.response.status);
        console.error('[DashboardApi] Response data:', error.response.data);
      }
      return 0;
    }
  }



  // Get comprehensive dashboard data
  static async getDashboardStats(userUuid?: string): Promise<DashboardStats> {
    try {
      console.log('[DashboardApi] Starting getDashboardStats with userUuid:', userUuid);
      
      // Check if we have a valid token before making API calls
      const token = localStorage.getItem('dido_token');
      if (!token || token.length < 10) {
        console.log('[DashboardApi] No valid token found, returning default stats');
        return {
          products: 0,
          warehouses: 0,
          securityEvents: [],
          userRole: 'user',
          warehouseName: 'Unknown'
        };
      }
      
      // Only fetch essential data - products and warehouses count
      const [productsCount, warehousesCount] = await Promise.all([
        this.getProductsCount(),
        this.getWarehousesCount(userUuid)
      ]);

      console.log('[DashboardApi] Essential API calls completed successfully:', {
        productsCount,
        warehousesCount
      });

      return {
        products: productsCount,
        warehouses: warehousesCount,
        securityEvents: [], // No security events needed for dashboard
        userRole: 'admin', // This should come from user context
        warehouseName: 'Main Warehouse' // This should come from user context
      };
    } catch (error: any) {
      console.error('[DashboardApi] Failed to fetch dashboard stats:', error);
      if (error.response) {
        console.error('[DashboardApi] Response status:', error.response.status);
        console.error('[DashboardApi] Response data:', error.response.data);
      }
      return {
        products: 0,
        warehouses: 0,
        securityEvents: [],
        userRole: 'user',
        warehouseName: 'Unknown'
      };
    }
  }
}

// Export individual functions for convenience
export const getProductsCount = () => DashboardApi.getProductsCount();
export const getWarehousesCount = (userUuid?: string) => DashboardApi.getWarehousesCount(userUuid);
export const getDashboardStats = (userUuid?: string) => DashboardApi.getDashboardStats(userUuid); 