import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { GoodsReceipt } from './goods-receipt.entity';

@Entity('goods_receipt_items')
export class GoodsReceiptItem {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the goods receipt item (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the goods receipt this item belongs to",
  })
  @Column('uuid')
  @Index()
  goodsReceiptUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase item this receipt item corresponds to",
  })
  @Column('uuid')
  @Index()
  purchaseItemUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @Column('uuid')
  @Index()
  productUuid: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of receipt",
  })
  @Column()
  name: string;

  @ApiProperty({
    example: 5,
    description: "Quantity ordered",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  quantityOrdered: number;

  @ApiProperty({
    example: 4,
    description: "Quantity received",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  quantityReceived: number;

  @ApiProperty({
    example: 1,
    description: "Quantity rejected/damaged",
    required: false,
  })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  quantityRejected: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of purchase",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  unitPrice: number;

  @ApiProperty({
    example: "Good condition",
    description: "Notes about the received items",
    required: false,
  })
  @Column({ nullable: true })
  notes?: string;

  @ApiProperty({
    example: 0,
    description: "Order of the item in the receipt (0-based index)",
    required: false,
  })
  @Column('int', { default: 0 })
  order: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to GoodsReceipt
  @ManyToOne(() => GoodsReceipt, goodsReceipt => goodsReceipt.id)
  @JoinColumn({ name: 'goodsReceiptUuid' })
  goodsReceipt: GoodsReceipt;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 