import { Injectable, Logger } from '@nestjs/common';
import { LogsService } from './logs.service';
import { CreateLogDto } from './dto/create-log.dto';

@Injectable()
export class LogsUtilityService {
  private readonly logger = new Logger(LogsUtilityService.name);

  constructor(private readonly logsService: LogsService) {}

  /**
   * Calculate delta changes between two objects
   * @param oldData - The original object
   * @param newData - The updated object
   * @returns Object containing only the changed fields with before/after values
   */
  private calculateDelta(oldData: any, newData: any): Record<string, { before: any; after: any }> {
    const delta: Record<string, { before: any; after: any }> = {};

    if (!oldData && !newData) {
      return delta;
    }

    // Handle case where oldData is null (creation)
    if (!oldData) {
      Object.keys(newData || {}).forEach(key => {
        if (newData[key] !== undefined && newData[key] !== null) {
          delta[key] = { before: null, after: newData[key] };
        }
      });
      return delta;
    }

    // Handle case where newData is null (deletion)
    if (!newData) {
      Object.keys(oldData || {}).forEach(key => {
        if (oldData[key] !== undefined && oldData[key] !== null) {
          delta[key] = { before: oldData[key], after: null };
        }
      });
      return delta;
    }

    // Compare all keys from both objects
    const allKeys = new Set([...Object.keys(oldData), ...Object.keys(newData)]);

    allKeys.forEach(key => {
      const oldValue = oldData[key];
      const newValue = newData[key];

      // Skip if values are the same
      if (this.deepEqual(oldValue, newValue)) {
        return;
      }

      // Skip internal fields that shouldn't be logged
      if (this.shouldSkipField(key)) {
        return;
      }

      delta[key] = { before: oldValue, after: newValue };
    });

    return delta;
  }

  /**
   * Deep equality check for comparing values
   */
  private deepEqual(a: any, b: any): boolean {
    if (a === b) return true;

    if (a == null || b == null) return a === b;

    if (typeof a !== typeof b) return false;

    if (typeof a === 'object') {
      if (Array.isArray(a) !== Array.isArray(b)) return false;

      if (Array.isArray(a)) {
        if (a.length !== b.length) return false;
        return a.every((item, index) => this.deepEqual(item, b[index]));
      }

      const keysA = Object.keys(a);
      const keysB = Object.keys(b);

      if (keysA.length !== keysB.length) return false;

      return keysA.every(key => this.deepEqual(a[key], b[key]));
    }

    return false;
  }

  /**
   * Check if a field should be skipped from delta logging
   */
  private shouldSkipField(fieldName: string): boolean {
    const skipFields = [
      'id', 'uuid', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy',
      'password', 'passwordHash', 'salt', 'token', 'refreshToken',
      '__v', '_id', 'version', 'paymentDate', // Added paymentDate since it's redundant with updatedAt
      'customerFiscalId', 'customerRc', 'customerArticleNumber' // Skip customer fiscal information
    ];

    return skipFields.includes(fieldName) || fieldName.startsWith('_');
  }

  private extractEntityType(entity: string): string {
    // Extract entity type from entity string (e.g., "purchase123" -> "purchase")
    const match = entity.match(/^([a-zA-Z]+)/);
    return match ? match[1] : 'unknown';
  }

  /**
   * Log a user action with the specified operation and entity
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed (e.g., 'created', 'updated', 'deleted', 'cancelled')
   * @param entity - The entity being affected (e.g., 'sale123', 'product456', 'customer789')
   */
  async logAction(userUuid: string, operation: string, entity: string): Promise<void> {
    try {
      const createLogDto: CreateLogDto = {
        userUuid,
        operation,
        entityType: this.extractEntityType(entity),
        entity,
      };

      await this.logsService.create(createLogDto);
      this.logger.debug(`Logged action: ${operation} on ${entity} by user ${userUuid}`);
    } catch (error) {
      this.logger.error(`Failed to log action: ${operation} on ${entity} by user ${userUuid}`, error);
      // Don't throw the error to avoid breaking the main operation
    }
  }

  /**
   * Log an action with delta changes instead of full object versions
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param entity - The entity being affected
   * @param description - Human-readable description
   * @param oldData - The original data (before changes)
   * @param newData - The updated data (after changes)
   * @param additionalData - Any additional data to include (will not be delta-calculated)
   */
  async logActionWithDelta(
    userUuid: string,
    operation: string,
    entity: string,
    description?: string,
    oldData?: any,
    newData?: any,
    additionalData?: Record<string, any>
  ): Promise<void> {
    try {
      const delta = this.calculateDelta(oldData, newData);

      const logData: Record<string, any> = {
        ...additionalData,
        changes: delta,
        changedFields: Object.keys(delta),
        changeCount: Object.keys(delta).length,
      };

      const createLogDto: CreateLogDto = {
        userUuid,
        operation,
        entityType: this.extractEntityType(entity),
        entity,
        description,
        data: logData,
      };

      await this.logsService.create(createLogDto);
      this.logger.debug(`Logged action with delta: ${operation} on ${entity} by user ${userUuid} (${Object.keys(delta).length} changes)`);
    } catch (error) {
      this.logger.error(`Failed to log action with delta: ${operation} on ${entity} by user ${userUuid}`, error);
      // Don't throw the error to avoid breaking the main operation
    }
  }

  /**
   * Log a creation action with only the new data
   * @param userUuid - The UUID of the user performing the action
   * @param entity - The entity being created
   * @param description - Human-readable description
   * @param newData - The created data
   * @param additionalData - Any additional data to include
   */
  async logCreation(
    userUuid: string,
    entity: string,
    description?: string,
    newData?: any,
    additionalData?: Record<string, any>
  ): Promise<void> {
    await this.logActionWithDelta(userUuid, 'created', entity, description, null, newData, additionalData);
  }

  /**
   * Log an update action with delta changes
   * @param userUuid - The UUID of the user performing the action
   * @param entity - The entity being updated
   * @param description - Human-readable description
   * @param oldData - The original data before update
   * @param newData - The updated data after update
   * @param additionalData - Any additional data to include
   */
  async logUpdate(
    userUuid: string,
    entity: string,
    description?: string,
    oldData?: any,
    newData?: any,
    additionalData?: Record<string, any>
  ): Promise<void> {
    await this.logActionWithDelta(userUuid, 'updated', entity, description, oldData, newData, additionalData);
  }

  /**
   * Log a deletion action with the deleted data
   * @param userUuid - The UUID of the user performing the action
   * @param entity - The entity being deleted
   * @param description - Human-readable description
   * @param deletedData - The data that was deleted
   * @param additionalData - Any additional data to include
   */
  async logDeletion(
    userUuid: string,
    entity: string,
    description?: string,
    deletedData?: any,
    additionalData?: Record<string, any>
  ): Promise<void> {
    await this.logActionWithDelta(userUuid, 'deleted', entity, description, deletedData, null, additionalData);
  }

  /**
   * Log a sale cancellation
   * @param userUuid - The UUID of the user cancelling the sale
   * @param saleId - The ID of the sale being cancelled
   */
  async logSaleCancellation(userUuid: string, saleId: string): Promise<void> {
    await this.logAction(userUuid, 'cancelled', `sale${saleId}`);
  }

  /**
   * Log a sale edit
   * @param userUuid - The UUID of the user editing the sale
   * @param saleId - The ID of the sale being edited
   */
  async logSaleEdit(userUuid: string, saleId: string): Promise<void> {
    await this.logAction(userUuid, 'edited', `sale${saleId}`);
  }

  /**
   * Log a sale creation
   * @param userUuid - The UUID of the user creating the sale
   * @param saleId - The ID of the sale being created
   */
  async logSaleCreation(userUuid: string, saleId: string): Promise<void> {
    await this.logAction(userUuid, 'created', `sale${saleId}`);
  }

  /**
   * Log a sale deletion
   * @param userUuid - The UUID of the user deleting the sale
   * @param saleId - The ID of the sale being deleted
   */
  async logSaleDeletion(userUuid: string, saleId: string): Promise<void> {
    await this.logAction(userUuid, 'deleted', `sale${saleId}`);
  }

  /**
   * Log a product action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param productId - The ID of the product being affected
   */
  async logProductAction(userUuid: string, operation: string, productId: string): Promise<void> {
    await this.logAction(userUuid, operation, `product${productId}`);
  }

  /**
   * Log a customer action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param customerId - The ID of the customer being affected
   */
  async logCustomerAction(userUuid: string, operation: string, customerId: string): Promise<void> {
    await this.logAction(userUuid, operation, `customer${customerId}`);
  }

  /**
   * Log an inventory action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param inventoryId - The ID of the inventory item being affected
   */
  async logInventoryAction(userUuid: string, operation: string, inventoryId: string): Promise<void> {
    await this.logAction(userUuid, operation, `inventory${inventoryId}`);
  }

  /**
   * Log a purchase action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param purchaseId - The ID of the purchase being affected
   */
  async logPurchaseAction(userUuid: string, operation: string, purchaseId: string): Promise<void> {
    await this.logAction(userUuid, operation, `purchase${purchaseId}`);
  }

  /**
   * Log a warehouse action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param warehouseId - The ID of the warehouse being affected
   */
  async logWarehouseAction(userUuid: string, operation: string, warehouseId: string): Promise<void> {
    await this.logAction(userUuid, operation, `warehouse${warehouseId}`);
  }

  /**
   * Log a van action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param vanId - The ID of the van being affected
   */
  async logVanAction(userUuid: string, operation: string, vanId: string): Promise<void> {
    await this.logAction(userUuid, operation, `van${vanId}`);
  }

  /**
   * Log a supplier action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param supplierId - The ID of the supplier being affected
   */
  async logSupplierAction(userUuid: string, operation: string, supplierId: string): Promise<void> {
    await this.logAction(userUuid, operation, `supplier${supplierId}`);
  }

  /**
   * Log a user action
   * @param userUuid - The UUID of the user performing the action
   * @param operation - The operation being performed
   * @param targetUserId - The ID of the user being affected
   */
  async logUserAction(userUuid: string, operation: string, targetUserId: string): Promise<void> {
    await this.logAction(userUuid, operation, `user${targetUserId}`);
  }
} 