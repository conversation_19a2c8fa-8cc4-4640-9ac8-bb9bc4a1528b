import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsNumber, IsUUID, IsBoolean, IsEnum, IsDateString, IsArray, ValidateNested, <PERSON>, <PERSON> } from "class-validator";
import { Type } from "class-transformer";
import { PaymentMethods, PurchaseStatus } from "../purchase.entity";

export class UpdatePurchaseItemDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase item",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  id?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  productUuid?: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of purchase",
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    example: 5,
    description: "Quantity to purchase",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0.01, { message: "Quantity must be greater than 0" })
  quantity?: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of purchase",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: "Unit price must be at least 0" })
  unitPrice?: number;

  @ApiProperty({
    example: 0,
    description: "Order of the item in the purchase (0-based index)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: "Order must be at least 0" })
  order?: number;
}

export class UpdatePurchaseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  supplierUuid?: string;

  @ApiProperty({
    example: "Supplier Name",
    description: "Supplier name at time of purchase",
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierName?: string;

  @ApiProperty({
    example: "12345678901",
    description: "Supplier fiscal ID at time of purchase",
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierFiscalId?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Supplier RC at time of purchase",
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierRc?: string;

  @ApiProperty({
    example: "ART001",
    description: "Supplier article number at time of purchase",
    required: false,
  })
  @IsOptional()
  @IsString()
  supplierArticleNumber?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse where goods will be received",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  warehouseUuid?: string;

  @ApiProperty({
    example: "Warehouse Name",
    description: "Warehouse name at time of purchase",
    required: false,
  })
  @IsOptional()
  @IsString()
  warehouseName?: string;

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this purchase",
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  useTax?: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0, { message: "Tax rate must be at least 0" })
  @Max(1, { message: "Tax rate must be at most 1" })
  taxRate?: number;

  @ApiProperty({
    example: PaymentMethods.BANK_TRANSFER,
    description: "Payment method used",
    enum: PaymentMethods,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentMethods)
  paymentMethod?: PaymentMethods;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Purchase date",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  purchaseDate?: string;

  @ApiProperty({
    example: "2025-02-14T10:30:00.000Z",
    description: "Due date for payment",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  dueDate?: string;

  @ApiProperty({
    example: PurchaseStatus.UNPAID,
    description: "Current status of the purchase",
    enum: PurchaseStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(PurchaseStatus)
  status?: PurchaseStatus;

  @ApiProperty({
    example: "Purchase for Q1 inventory restocking",
    description: "Notes or comments about the purchase",
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the purchase",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  updatedBy?: string;

  @ApiProperty({
    example: [{
      id: "uuid-v7-string",
      productUuid: "uuid-v7-string",
      name: "Product Name",
      quantity: 5,
      unitPrice: 25.99,
      order: 0
    }],
    description: "Array of purchase items",
    type: [UpdatePurchaseItemDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdatePurchaseItemDto)
  purchaseItems?: UpdatePurchaseItemDto[];
} 