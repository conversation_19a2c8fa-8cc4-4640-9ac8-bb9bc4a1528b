import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOrderFieldToSaleItems1753714990076 implements MigrationInterface {
    name = 'AddOrderFieldToSaleItems1753714990076'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "sale_items" ADD "order" integer NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "sale_items" DROP COLUMN "order"`);
    }

}
