import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  ParseUUIDPipe,
} from "@nestjs/common";
import { QuoteService } from "./quote.service";
import { Quote } from "./quote.entity";
import { QuoteItem } from "./quote-item.entity";
import { CreateQuoteDto } from "./dto/create-quote.dto";
import { ApiTags } from "@nestjs/swagger";

@ApiTags("quotes")
@Controller("quotes")
export class QuoteController {
  constructor(private readonly quoteService: QuoteService) {}

  @Post()
  async create(@Body() body: CreateQuoteDto) {
    return this.quoteService.create(body);
  }

  @Get()
  async findAll() {
    return this.quoteService.findAll();
  }

  @Get(":uuid")
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string) {
    return this.quoteService.findOne(uuid);
  }

  @Put(":uuid")
  async update(@Param("uuid", ParseUUIDPipe) uuid: string, @Body() body: Partial<Quote>) {
    return this.quoteService.update(uuid, body);
  }

  @Delete(":uuid")
  async remove(@Param("uuid", ParseUUIDPipe) uuid: string) {
    await this.quoteService.remove(uuid);
    return { deleted: true };
  }

  @Post(":uuid/items")
  async addItemsToQuote(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() items: QuoteItem[],
  ) {
    return this.quoteService.addItemsToQuote(uuid, items);
  }

  @Get("list-by-warehouse/:warehouseUuid")
  async listByWarehouse(@Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string) {
    return this.quoteService.findByWarehouse(warehouseUuid);
  }
}
