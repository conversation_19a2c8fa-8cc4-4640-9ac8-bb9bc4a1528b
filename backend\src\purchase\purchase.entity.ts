import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, OneToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { PurchaseItem } from './purchase-item.entity';

export enum PurchaseStatus {
  DRAFT = "draft",
  PENDING_APPROVAL = "pending_approval",
  APPROVED = "approved",
  ORDERED = "ordered",
  PARTIALLY_RECEIVED = "partially_received",
  RECEIVED = "received",
  FULLY_RECEIVED = "fully_received",
  UNPAID = "unpaid",
  PARTIALLY_PAID = "partially_paid",
  PAID = "paid",
  CANCELLED = "cancelled",
  RETURNED = "returned",
}

export enum PaymentMethods {
  CASH = "cash",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  MOBILE_PAYMENT = "mobile_payment",
  CHEQUE = "cheque",
  CREDIT_TERMS = "credit_terms",
  OTHER = "other",
}

@Entity('purchases')
export class Purchase {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "PO-********-001",
    description: "Unique purchase number",
  })
  @Column({ unique: true })
  purchaseNumber: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  supplierUuid?: string;

  @ApiProperty({
    example: "Supplier Name",
    description: "Supplier name at time of purchase",
    required: false,
  })
  @Column({ nullable: true })
  supplierName?: string;

  @ApiProperty({
    example: "***********",
    description: "Supplier fiscal ID at time of purchase",
    required: false,
  })
  @Column({ nullable: true })
  supplierFiscalId?: string;

  @ApiProperty({
    example: "RC123456",
    description: "Supplier RC at time of purchase",
    required: false,
  })
  @Column({ nullable: true })
  supplierRc?: string;

  @ApiProperty({
    example: "ART001",
    description: "Supplier article number at time of purchase",
    required: false,
  })
  @Column({ nullable: true })
  supplierArticleNumber?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse where goods will be received",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  warehouseUuid?: string;

  @ApiProperty({
    example: "Warehouse Name",
    description: "Warehouse name at time of purchase",
    required: false,
  })
  @Column({ nullable: true })
  warehouseName?: string;

  @ApiProperty({ example: 150.0, description: "Subtotal amount before tax" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  subtotal: number;

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this purchase",
  })
  @Column({ default: false })
  useTax: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
  })
  @Column('decimal', { precision: 5, scale: 4, default: 0.1 })
  taxRate: number;

  @ApiProperty({ example: 15.0, description: "Total tax amount" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @ApiProperty({ example: 165.0, description: "Total amount including tax" })
  @Column('decimal', { precision: 10, scale: 2 })
  totalAmount: number;

  @ApiProperty({ example: 100.0, description: "Amount paid so far" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  amountPaid: number;

  @ApiProperty({ example: 65.0, description: "Balance due to supplier" })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  balanceDue: number;

  @ApiProperty({
    example: PaymentMethods.BANK_TRANSFER,
    description: "Payment method used",
    enum: PaymentMethods,
  })
  @Column({
    type: 'enum',
    enum: PaymentMethods,
    default: PaymentMethods.BANK_TRANSFER,
  })
  paymentMethod: PaymentMethods;

  @ApiProperty({
    example: ["2025-01-15T10:30:00.000Z"],
    description: "Array of payment dates (excluded from logs since redundant with updatedAt)",
    required: false,
  })
  @Column('jsonb', { nullable: true })
  paymentDate?: Date[];

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Purchase date",
  })
  @Column()
  purchaseDate: Date;

  @ApiProperty({
    example: "2025-02-14T10:30:00.000Z",
    description: "Due date for payment",
  })
  @Column()
  dueDate: Date;

  @ApiProperty({
    example: "2025-02-20T10:30:00.000Z",
    description: "Expected delivery date from supplier",
    required: false,
  })
  @Column({ nullable: true })
  expectedDeliveryDate?: Date;

  @ApiProperty({
    example: "2025-02-25T10:30:00.000Z",
    description: "Actual delivery date when goods were received",
    required: false,
  })
  @Column({ nullable: true })
  actualDeliveryDate?: Date;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the related order",
    required: false,
  })
  @Column('uuid', { nullable: true })
  orderUuid?: string;

  @ApiProperty({
    example: PurchaseStatus.UNPAID,
    description: "Current status of the purchase",
    enum: PurchaseStatus,
  })
  @Column({
    type: 'enum',
    enum: PurchaseStatus,
    default: PurchaseStatus.UNPAID,
  })
  status: PurchaseStatus;

  @ApiProperty({
    example: "Purchase for Q1 inventory restocking",
    description: "Notes or comments about the purchase",
    required: false,
  })
  @Column({ nullable: true })
  notes?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the purchase",
  })
  @Column('uuid')
  @Index()
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the purchase",
  })
  @Column('uuid')
  @Index()
  updatedBy: string;

  @ApiProperty({
    example: false,
    description: "Whether the purchase has been soft deleted",
  })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to PurchaseItems
  @OneToMany(() => PurchaseItem, purchaseItem => purchaseItem.purchase)
  purchaseItems: PurchaseItem[];

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 