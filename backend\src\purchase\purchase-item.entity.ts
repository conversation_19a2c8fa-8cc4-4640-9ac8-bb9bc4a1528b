import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { Purchase } from './purchase.entity';

@Entity('purchase_items')
export class PurchaseItem {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase item (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase this item belongs to",
  })
  @Column('uuid')
  @Index()
  purchaseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @Column('uuid')
  @Index()
  productUuid: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of purchase",
  })
  @Column()
  name: string;

  @ApiProperty({
    example: 5,
    description: "Quantity purchased",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  quantity: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of purchase",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  unitPrice: number;

  @ApiProperty({
    example: 129.95,
    description: "Line total (quantity * unit price)",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  lineTotal: number;

  @ApiProperty({
    example: 5.25,
    description: "Tax amount for this line item",
    required: false,
  })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @ApiProperty({
    example: 0,
    description: "Order of the item in the purchase order (0-based index)",
    required: false,
  })
  @Column('int', { default: 0 })
  order: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to Purchase
  @ManyToOne(() => Purchase, purchase => purchase.purchaseItems)
  @JoinColumn({ name: 'purchaseUuid' })
  purchase: Purchase;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 