import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { StockComputationService } from "./stock-computation.service";
import { StockComputationController } from "./stock-computation.controller";
import { InventoryItem } from "../inventory/inventory-item.entity";
import { StockAdjustment } from "../inventory/stock-adjustment.entity";
import { Quote } from "../quotes/quote.entity";
import { QuoteItem } from "../quotes/quote-item.entity";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      InventoryItem,
      StockAdjustment,
      Quote,
      QuoteItem,
    ]),
  ],
  controllers: [StockComputationController],
  providers: [StockComputationService],
  exports: [StockComputationService],
})
export class StockComputationModule {}
