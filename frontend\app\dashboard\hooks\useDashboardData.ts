import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { getDashboardStats, DashboardStats } from '../dashboardApi';

export const useDashboardData = () => {
  const { user, token, loading: authLoading } = useAuth();
  const userUuid = user?.uuid;

  // Check if token is actually valid (not just exists)
  const isTokenValid = token && token.length > 10; // Basic validation
  const shouldEnableQuery = !!userUuid && !!token && isTokenValid && !authLoading;

  console.log('[useDashboardData] Hook called with:', {
    hasUser: !!user,
    userUuid,
    hasToken: !!token,
    tokenLength: token?.length,
    isTokenValid,
    authLoading,
    shouldEnableQuery
  });

  const { data: dashboardStats, isLoading, error, refetch } = useQuery({
    queryKey: ['dashboard', userUuid],
    queryFn: () => {
      console.log('[useDashboardData] Query function called with userUuid:', userUuid);
      return getDashboardStats(userUuid);
    },
    enabled: shouldEnableQuery,
    refetchInterval: shouldEnableQuery ? 30000 : false, // Only refetch if enabled
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  console.log('[useDashboardData] Query state:', {
    isLoading,
    hasError: !!error,
    hasData: !!dashboardStats,
    errorMessage: error?.message
  });

  // Get user role from context or default to 'user'
  const userRole = user?.roles?.[0]?.name || 'user';
  const warehouseName = user?.warehouseName || 'Unknown Warehouse';

  // Role-based dashboard configuration
  const getDashboardCards = () => {
    const baseCards = [
      {
        title: 'Products',
        value: dashboardStats?.products || 0,
        icon: '📦',
        color: 'blue',
        link: '/inventory/products'
      },
      {
        title: 'Warehouses',
        value: dashboardStats?.warehouses || 0,
        icon: '🏭',
        color: 'teal',
        link: '/logistics/warehouses'
      }
    ];

    // Admin dashboard cards
    if (userRole === 'admin') {
      return [
        ...baseCards,
        {
          title: 'Security Events',
          value: dashboardStats?.securityEvents?.length || 0,
          icon: '🔒',
          color: 'purple',
          link: '/settings/security'
        },
        {
          title: 'Active Sessions',
          value: '3', // This would come from a sessions endpoint
          icon: '👥',
          color: 'indigo',
          link: '/settings/users'
        }
      ];
    }

    // Manager dashboard cards
    if (userRole === 'manager') {
      return [
        ...baseCards,
        {
          title: 'Team Performance',
          value: '87%',
          icon: '📊',
          color: 'purple',
          link: '/reports/team'
        },
        {
          title: 'Low Stock Alerts',
          value: '5', // This would come from inventory alerts
          icon: '⚠️',
          color: 'orange',
          link: '/inventory/stock-levels'
        }
      ];
    }

    // Mobile Sale Agent dashboard cards
    if (userRole === 'mobile_sale_agent') {
      return [
        {
          title: 'My Sales',
          value: '$2,345', // This would come from sales data
          icon: '💰',
          color: 'green',
          link: '/sales/pos'
        },
        {
          title: 'Clients',
          value: '18', // This would come from customers data
          icon: '👥',
          color: 'blue',
          link: '/sales/customers'
        },
        {
          title: 'Missions',
          value: '5', // This would come from tasks/assignments
          icon: '📋',
          color: 'orange',
          link: '/logistics/routes'
        },
        {
          title: 'Messages',
          value: '3', // This would come from messaging system
          icon: '💬',
          color: 'purple',
          link: '/communications'
        }
      ];
    }

    // Default cards for other roles
    return baseCards;
  };

  // Quick actions based on user role
  const getQuickActions = () => {
    const baseActions = [
      {
        title: 'Add Product',
        icon: '➕',
        link: '/inventory/products',
        color: 'blue'
      },
      {
        title: 'Create Sale',
        icon: '💰',
        link: '/sales/pos',
        color: 'green'
      }
    ];

    if (userRole === 'admin') {
      return [
        ...baseActions,
        {
          title: 'Manage Users',
          icon: '👥',
          link: '/settings/users',
          color: 'purple'
        },
        {
          title: 'System Settings',
          icon: '⚙️',
          link: '/settings/system',
          color: 'gray'
        }
      ];
    }

    if (userRole === 'manager') {
      return [
        ...baseActions,
        {
          title: 'View Reports',
          icon: '📊',
          link: '/reports',
          color: 'purple'
        },
        {
          title: 'Stock Levels',
          icon: '📦',
          link: '/inventory/stock-levels',
          color: 'orange'
        }
      ];
    }

    if (userRole === 'mobile_sale_agent') {
      return [
        {
          title: 'Start Sale',
          icon: '💰',
          link: '/sales/pos',
          color: 'green'
        },
        {
          title: 'View Customers',
          icon: '👥',
          link: '/sales/customers',
          color: 'blue'
        },
        {
          title: 'Check Routes',
          icon: '🗺️',
          link: '/logistics/routes',
          color: 'purple'
        },
        {
          title: 'Messages',
          icon: '💬',
          link: '/communications',
          color: 'teal'
        }
      ];
    }

    return baseActions;
  };

  return {
    // Data
    dashboardStats: dashboardStats || {
      products: 0,
      warehouses: 0,
      securityEvents: [],
      userRole: 'user',
      warehouseName: 'Unknown'
    },
    
    // State
    isLoading,
    error,
    
    // Computed
    userRole,
    warehouseName,
    dashboardCards: getDashboardCards(),
    quickActions: getQuickActions(),
    
    // Actions
    refetch
  };
}; 