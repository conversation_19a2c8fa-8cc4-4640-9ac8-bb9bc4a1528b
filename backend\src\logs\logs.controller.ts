import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Query,
  ParseUUIDPipe,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { LogsService } from './logs.service';
import { CreateLogDto } from './dto/create-log.dto';
import { FilterLogsDto } from './dto/filter-logs.dto';
import { LogResponseDto } from './dto/log-response.dto';
import { PaginatedResponseDto } from '../dto/pagination.dto';
import { Log } from './log.entity';
import { LOG_OPERATIONS_ARRAY, LOG_ENTITIES_ARRAY } from './logs.constants';

@ApiTags('logs')
@Controller('logs')
export class LogsController {
  constructor(private readonly logsService: LogsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new log entry' })
  @ApiResponse({
    status: 201,
    description: 'Log entry created successfully',
    type: LogResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  async create(@Body() createLogDto: CreateLogDto): Promise<Log> {
    return await this.logsService.create(createLogDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all log entries with optional filtering and pagination' })
  @ApiResponse({
    status: 200,
    description: 'Log entries retrieved successfully',
    type: PaginatedResponseDto,
  })
  async findAll(@Query() filterDto: FilterLogsDto): Promise<PaginatedResponseDto<Log>> {
    return await this.logsService.findAll(filterDto);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific log entry by ID' })
  @ApiResponse({
    status: 200,
    description: 'Log entry retrieved successfully',
    type: LogResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Log entry not found',
  })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<Log> {
    return await this.logsService.findOne(id);
  }

  @Get('operations/available')
  @ApiOperation({ summary: 'Get all available operations' })
  @ApiResponse({
    status: 200,
    description: 'Available operations retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        operations: {
          type: 'array',
          items: { type: 'string' },
          example: ['create', 'update', 'delete']
        }
      }
    }
  })
  async getAvailableOperations(): Promise<{ operations: string[] }> {
    return { operations: LOG_OPERATIONS_ARRAY };
  }

  @Get('entities/available')
  @ApiOperation({ summary: 'Get all available entities' })
  @ApiResponse({
    status: 200,
    description: 'Available entities retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        entities: {
          type: 'array',
          items: { type: 'string' },
          example: ['sale', 'purchase', 'region', 'route', 'product', 'van', 'warehouse', 'stock_adjustment', 'supplier', 'order', 'customer', 'user', 'company', 'inventory_item', 'product_category', 'account_settings', 'credit_adjustment', 'payment', 'log', 'auth_audit', 'user_account_plan', 'feature']
        }
      }
    }
  })
  async getAvailableEntities(): Promise<{ entities: string[] }> {
    return { entities: LOG_ENTITIES_ARRAY };
  }

  @Delete('all')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'DANGEROUS: Hard delete all logs from database',
    description: `
      ⚠️ DANGEROUS OPERATION - NOT FOR PRODUCTION USE ⚠️
      
      This endpoint permanently deletes ALL logs from the database.
      This action is IRREVERSIBLE and will result in complete loss of audit trail.
      
      WARNINGS:
      - This operation cannot be undone
      - All audit history will be permanently lost
      - This may violate compliance requirements
      - Use only in development/testing environments
      - Consider backing up data before proceeding
      
      If you need to clear logs in production, consider:
      - Using soft deletes instead
      - Implementing log rotation/archiving
      - Setting up log retention policies
      
      This endpoint is provided for development and testing purposes only.
    `
  })
  @ApiResponse({
    status: 200,
    description: 'All logs have been permanently deleted from the database',
    schema: {
      type: 'object',
      properties: {
        deletedCount: {
          type: 'number',
          description: 'Number of logs that were deleted',
          example: 1500
        },
        message: {
          type: 'string',
          description: 'Confirmation message with warning',
          example: 'Successfully deleted 1500 logs from database. This action is irreversible.'
        }
      }
    }
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during deletion process'
  })
  async deleteAll(): Promise<{ deletedCount: number; message: string }> {
    return await this.logsService.deleteAll();
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a log entry by ID' })
  @ApiResponse({
    status: 204,
    description: 'Log entry deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Log entry not found',
  })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    return await this.logsService.remove(id);
  }
} 