/**
 * Example: How to add a new entity type to the logs display system
 * 
 * This file shows how easy it is to add support for a new entity type.
 * Simply add a new configuration to the entityDisplayConfigs object.
 */

import type { Log } from '../../logsApi';
import type { EntityDisplayConfig } from '../entityDisplayConfig';

// Example: Adding support for "invoice" entities
export const invoiceEntityConfig: EntityDisplayConfig = {
  getDisplayName: (log: Log) => log.data?.invoiceNumber || 'Invoice',
  getSubtitle: (log: Log) => log.entity.replace('invoice_', ''),
  getIcon: () => '📄',
  getColor: () => 'text-blue-600',
  getDescription: (log: Log) => {
    const customer = log.data?.customerName;
    const amount = log.data?.totalAmount;
    const status = log.data?.status;
    
    const parts = [];
    if (customer) parts.push(customer);
    if (amount) parts.push(`$${amount}`);
    if (status) parts.push(status);
    
    return parts.length > 0 ? parts.join(' • ') : undefined;
  },
  getMetadata: (log: Log) => {
    const metadata = [];
    
    if (log.data?.customerName) {
      metadata.push({ 
        label: 'Customer', 
        value: log.data.customerName, 
        color: 'text-indigo-600' 
      });
    }
    
    if (log.data?.totalAmount) {
      metadata.push({ 
        label: 'Amount', 
        value: `$${log.data.totalAmount}`, 
        color: 'text-green-600' 
      });
    }
    
    if (log.data?.dueDate) {
      metadata.push({ 
        label: 'Due Date', 
        value: new Date(log.data.dueDate).toLocaleDateString(), 
        color: 'text-orange-600' 
      });
    }
    
    if (log.data?.status) {
      metadata.push({ 
        label: 'Status', 
        value: log.data.status, 
        color: 'text-purple-600' 
      });
    }
    
    return metadata;
  },
};

// Example: Adding support for "shipment" entities
export const shipmentEntityConfig: EntityDisplayConfig = {
  getDisplayName: (log: Log) => log.data?.trackingNumber || 'Shipment',
  getSubtitle: (log: Log) => log.entity.replace('shipment_', ''),
  getIcon: () => '📦',
  getColor: () => 'text-amber-600',
  getDescription: (log: Log) => {
    const carrier = log.data?.carrier;
    const destination = log.data?.destination;
    const status = log.data?.status;
    
    const parts = [];
    if (carrier) parts.push(carrier);
    if (destination) parts.push(`to ${destination}`);
    if (status) parts.push(status);
    
    return parts.length > 0 ? parts.join(' • ') : undefined;
  },
  getMetadata: (log: Log) => {
    const metadata = [];
    
    if (log.data?.carrier) {
      metadata.push({ 
        label: 'Carrier', 
        value: log.data.carrier, 
        color: 'text-amber-600' 
      });
    }
    
    if (log.data?.destination) {
      metadata.push({ 
        label: 'Destination', 
        value: log.data.destination, 
        color: 'text-blue-600' 
      });
    }
    
    if (log.data?.estimatedDelivery) {
      metadata.push({ 
        label: 'ETA', 
        value: new Date(log.data.estimatedDelivery).toLocaleDateString(), 
        color: 'text-green-600' 
      });
    }
    
    if (log.data?.weight) {
      metadata.push({ 
        label: 'Weight', 
        value: `${log.data.weight} lbs`, 
        color: 'text-gray-600' 
      });
    }
    
    return metadata;
  },
};

// Example: Adding support for "contract" entities
export const contractEntityConfig: EntityDisplayConfig = {
  getDisplayName: (log: Log) => log.data?.contractNumber || 'Contract',
  getSubtitle: (log: Log) => log.entity.replace('contract_', ''),
  getIcon: () => '📋',
  getColor: () => 'text-emerald-600',
  getDescription: (log: Log) => {
    const client = log.data?.clientName;
    const value = log.data?.contractValue;
    const term = log.data?.termLength;
    
    const parts = [];
    if (client) parts.push(client);
    if (value) parts.push(`$${value}`);
    if (term) parts.push(`${term} months`);
    
    return parts.length > 0 ? parts.join(' • ') : undefined;
  },
  getMetadata: (log: Log) => {
    const metadata = [];
    
    if (log.data?.clientName) {
      metadata.push({ 
        label: 'Client', 
        value: log.data.clientName, 
        color: 'text-emerald-600' 
      });
    }
    
    if (log.data?.contractValue) {
      metadata.push({ 
        label: 'Value', 
        value: `$${log.data.contractValue}`, 
        color: 'text-green-600' 
      });
    }
    
    if (log.data?.startDate) {
      metadata.push({ 
        label: 'Start Date', 
        value: new Date(log.data.startDate).toLocaleDateString(), 
        color: 'text-blue-600' 
      });
    }
    
    if (log.data?.status) {
      metadata.push({ 
        label: 'Status', 
        value: log.data.status, 
        color: 'text-purple-600' 
      });
    }
    
    return metadata;
  },
};

/**
 * To use these configurations, simply add them to the entityDisplayConfigs object
 * in entityDisplayConfig.ts:
 * 
 * export const entityDisplayConfigs: Record<string, EntityDisplayConfig> = {
 *   // ... existing configurations
 *   invoice: invoiceEntityConfig,
 *   shipment: shipmentEntityConfig,
 *   contract: contractEntityConfig,
 *   // ... rest of configurations
 * };
 */

/**
 * Tips for creating new entity configurations:
 * 
 * 1. Choose meaningful icons that represent the entity type
 * 2. Use consistent color schemes that fit your design system
 * 3. Prioritize the most important information in getDisplayName and getDescription
 * 4. Handle missing data gracefully with fallbacks
 * 5. Keep metadata concise (2-4 items max) to avoid clutter
 * 6. Use appropriate Tailwind CSS color classes
 * 7. Consider the context where the information will be displayed
 */
