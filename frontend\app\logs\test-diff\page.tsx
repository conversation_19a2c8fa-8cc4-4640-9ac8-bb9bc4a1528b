'use client';

import React, { useState } from 'react';
import { LogDetailsModal } from '../components/LogDetailsModal';
import type { Log } from '../logsApi';

export default function TestDiffPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState<Log | null>(null);

  // Sample log data with delta changes
  const sampleLogs: Log[] = [
    {
      id: '01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2e',
      userUuid: '01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2f',
      operation: 'updated',
      entity: 'sale_123',
      description: 'Sale updated with new customer and payment info',
      createdAt: new Date('2025-01-15T10:30:00.000Z'),
      data: {
        saleUuid: 'sale_123',
        customerName: '<PERSON>',
        userName: 'Admin User',
        updatedAt: '2025-01-15T10:30:00.000Z',
        changes: {
          customerName: {
            before: '<PERSON>',
            after: '<PERSON>'
          },
          status: {
            before: 'pending',
            after: 'completed'
          },
          amountPaid: {
            before: 0,
            after: 150.00
          },
          items: {
            before: [
              { id: 1, name: 'Product A', quantity: 2, price: 50 },
              { id: 2, name: 'Product B', quantity: 1, price: 30 }
            ],
            after: [
              { id: 1, name: 'Product A', quantity: 3, price: 50 },
              { id: 2, name: 'Product B', quantity: 1, price: 30 },
              { id: 3, name: 'Product C', quantity: 1, price: 20 }
            ]
          },
          metadata: {
            before: {
              source: 'web',
              version: '1.0',
              tags: ['urgent']
            },
            after: {
              source: 'mobile',
              version: '1.1',
              tags: ['urgent', 'priority'],
              notes: 'Updated via mobile app'
            }
          }
        },
        changedFields: ['customerName', 'status', 'amountPaid', 'items', 'metadata'],
        changeCount: 5
      }
    },
    {
      id: '01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e3e',
      userUuid: '01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2f',
      operation: 'created',
      entity: 'product_456',
      description: 'New product created',
      createdAt: new Date('2025-01-15T11:00:00.000Z'),
      data: {
        productUuid: 'product_456',
        userName: 'Admin User',
        createdAt: '2025-01-15T11:00:00.000Z',
        changes: {
          name: {
            before: null,
            after: 'New Awesome Product'
          },
          price: {
            before: null,
            after: 99.99
          },
          category: {
            before: null,
            after: 'Electronics'
          }
        },
        changedFields: ['name', 'price', 'category'],
        changeCount: 3
      }
    },
    {
      id: '01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e4e',
      userUuid: '01890b6e-7b8c-7e6b-8e2e-7b8c7e6b8e2f',
      operation: 'deleted',
      entity: 'customer_789',
      description: 'Customer deleted',
      createdAt: new Date('2025-01-15T12:00:00.000Z'),
      data: {
        customerUuid: 'customer_789',
        userName: 'Admin User',
        deletedAt: '2025-01-15T12:00:00.000Z',
        changes: {
          name: {
            before: 'Old Customer',
            after: null
          },
          email: {
            before: '<EMAIL>',
            after: null
          },
          status: {
            before: 'active',
            after: null
          }
        },
        changedFields: ['name', 'email', 'status'],
        changeCount: 3
      }
    }
  ];

  const openModal = (log: Log) => {
    setSelectedLog(log);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedLog(null);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Delta Diff Viewer Test
        </h1>
        
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">
              Sample Logs with Delta Changes
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Click on any log to see the new diff viewer in action
            </p>
          </div>
          
          <div className="divide-y divide-gray-200">
            {sampleLogs.map((log) => (
              <div
                key={log.id}
                className="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => openModal(log)}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">
                      {log.description}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {log.operation} • {log.entity} • {log.data?.changeCount || 0} changes
                    </p>
                  </div>
                  <div className="text-xs text-gray-500">
                    {log.createdAt.toLocaleString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Features Demonstrated
          </h2>
          <ul className="space-y-2 text-sm text-gray-600">
            <li>• <strong>Before/After Comparison:</strong> Inline view with strikethrough for removed content and highlighting for added content</li>
            <li>• <strong>Complex Objects:</strong> JSON objects and arrays are properly displayed</li>
            <li>• <strong>Null Handling:</strong> Creation and deletion scenarios with null values</li>
            <li>• <strong>Visual Clarity:</strong> Clear distinction between unchanged, added, and removed content</li>
            <li>• <strong>Compact Storage:</strong> Only changed fields are stored, reducing log size significantly</li>
          </ul>
        </div>
      </div>

      <LogDetailsModal
        isOpen={isModalOpen}
        log={selectedLog}
        onClose={closeModal}
        userName="Admin User"
      />
    </div>
  );
}
