// customerPaymentsApi.ts - API utilities for Customer Payments endpoints
// Follows project convention: All API calls use /api/ prefix for Next.js proxying
// Based on backend/src/customers/customer-payment.controller.ts endpoints
// Integrated with latest backend/src/customers/customer.controller.ts endpoints

import axios from 'axios';

const API_BASE = '/api';

// Utility function for formatting currency


// Payment method enum matching backend
export type PaymentMethod = 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'check' | 'mobile_payment';

// Payment status enum matching backend
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';

// Customer Payment interface matching backend CustomerPaymentResponseDto
export interface CustomerPayment {
  uuid: string;
  customerUuid: string;
  userUuid: string;
  warehouseUuid: string;
  saleUuid?: string;
  paymentMethod: PaymentMethod;
  amount: number;
  description?: string;
  referenceNumber?: string;
  status: PaymentStatus;
  processedAt?: Date;
  isDeleted: boolean;
  createdAt: Date;
  updatedAt: Date;
  // Populated fields from backend
  customerName?: string;
  userName?: string;
  warehouseName?: string;
  saleInvoiceNumber?: string;
}

// Customer interface for enhanced payment modal
export interface Customer {
  uuid: string;
  name: string;
  fiscalId: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
  latitude?: number;
  longitude?: number;
  warehouseUuidString?: string;
  regionUuidString?: string;
  isDeleted: boolean;
  currentCredit: number;
  createdAt: Date;
  updatedAt: Date;
}

// Customer Sales interface for context
export interface CustomerSale {
  uuid: string;
  invoiceNumber: string;
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
  paymentMethod: string;
  status: string;
  invoiceDate: Date;
  dueDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Create customer payment DTO matching backend CreateCustomerPaymentDto
export interface CreateCustomerPaymentDto {
  customerUuid: string;
  userUuid: string;
  warehouseUuid: string;
  saleUuid?: string;
  paymentMethod: PaymentMethod;
  amount: number;
  description?: string;
  referenceNumber?: string;
}

// Update customer payment DTO matching backend UpdateCustomerPaymentDto
export interface UpdateCustomerPaymentDto {
  amount?: number;
  description?: string;
  referenceNumber?: string;
  status?: PaymentStatus;
  processedAt?: string; // ISO date string
  userUuid?: string; // For tracking who made the update
}

// Filter interface matching backend FilterCustomerPaymentDto
export interface FilterCustomerPaymentDto {
  customerUuid?: string;
  userUuid?: string;
  warehouseUuid?: string;
  saleUuid?: string;
  paymentMethod?: PaymentMethod;
  status?: PaymentStatus;
  minAmount?: number;
  maxAmount?: number;
  description?: string;
  referenceNumber?: string;
  fromDate?: string; // ISO date string
  toDate?: string; // ISO date string
  isDeleted?: boolean;
}

// Pagination DTO
export interface PaginationQueryDto {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Paginated response DTO - matches backend structure with nested meta
export interface PaginatedResponseDto<T> {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Credit adjustment response for refunds
export interface CreditAdjustmentResponseDto {
  uuid: string;
  customerUuid: string;
  userUuid: string;
  warehouseUuid: string;
  type: string;
  amount: number;
  reason: string;
  referenceType: string;
  referenceUuid: string;
  createdAt: Date;
}

// Refund response
export interface RefundResponse {
  payment: CustomerPayment;
  creditAdjustment: CreditAdjustmentResponseDto;
}

// Create a new customer payment
// POST /api/customer-payments
export async function createCustomerPayment(
  createDto: CreateCustomerPaymentDto
): Promise<CustomerPayment> {
  try {
    const response = await axios.post(`${API_BASE}/customer-payments`, createDto);
    return response.data;
  } catch (error: any) {
    console.error('Create customer payment error:', error.response?.data || error.message);
    throw error;
  }
}

// Get all customer payments with pagination and filtering
// GET /api/customer-payments?page=1&limit=10&...filters
export async function getCustomerPayments(
  paginationQuery: PaginationQueryDto = { page: 1, limit: 10 },
  filter?: FilterCustomerPaymentDto
): Promise<PaginatedResponseDto<CustomerPayment>> {
  try {
    const params = new URLSearchParams();
    
    // Add pagination parameters
    params.append('page', (paginationQuery.page || 1).toString());
    params.append('limit', (paginationQuery.limit || 10).toString());
    
    if (paginationQuery.sortBy) params.append('sortBy', paginationQuery.sortBy);
    if (paginationQuery.sortOrder) params.append('sortOrder', paginationQuery.sortOrder);

    // Add filter parameters if provided
    if (filter) {
      if (filter.customerUuid) params.append('customerUuid', filter.customerUuid);
      if (filter.userUuid) params.append('userUuid', filter.userUuid);
      if (filter.warehouseUuid) params.append('warehouseUuid', filter.warehouseUuid);
      if (filter.saleUuid) params.append('saleUuid', filter.saleUuid);
      if (filter.paymentMethod) params.append('paymentMethod', filter.paymentMethod);
      if (filter.status) params.append('status', filter.status);
      if (filter.minAmount !== undefined) params.append('minAmount', filter.minAmount.toString());
      if (filter.maxAmount !== undefined) params.append('maxAmount', filter.maxAmount.toString());
      if (filter.description) params.append('description', filter.description);
      if (filter.referenceNumber) params.append('referenceNumber', filter.referenceNumber);
      if (filter.fromDate) params.append('fromDate', filter.fromDate);
      if (filter.toDate) params.append('toDate', filter.toDate);
      if (filter.isDeleted !== undefined) params.append('isDeleted', filter.isDeleted.toString());
    }

    const url = `${API_BASE}/customer-payments?${params.toString()}`;

    const response = await axios.get(url);
    return response.data;
  } catch (error: any) {
    console.error('Get customer payments error:', error.response?.data || error.message);
    throw error;
  }
}

// Advanced filtering using POST endpoint
// POST /api/customer-payments/filter
export async function filterCustomerPayments(
  filterDto: FilterCustomerPaymentDto
): Promise<CustomerPayment[]> {
  try {
    const response = await axios.post(`${API_BASE}/customer-payments/filter`, filterDto);
    return response.data;
  } catch (error: any) {
    console.error('Filter customer payments error:', error.response?.data || error.message);
    throw error;
  }
}

// Get payments for a specific customer
// GET /api/customer-payments/customer/:customerUuid
export async function getPaymentsByCustomer(
  customerUuid: string,
  page: number = 1,
  limit: number = 10
): Promise<PaginatedResponseDto<CustomerPayment>> {
  try {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    const url = `${API_BASE}/customer-payments/customer/${customerUuid}?${params.toString()}`;

    const response = await axios.get(url);
    return response.data;
  } catch (error: any) {
    console.error('Get payments by customer error:', error.response?.data || error.message);
    throw error;
  }
}

// Get a single customer payment by UUID
// GET /api/customer-payments/:uuid
export async function getCustomerPaymentByUuid(uuid: string): Promise<CustomerPayment> {
  try {
    const response = await axios.get(`${API_BASE}/customer-payments/${uuid}`);
    return response.data;
  } catch (error: any) {
    console.error('Get customer payment by UUID error:', error.response?.data || error.message);
    throw error;
  }
}

// Update a customer payment
// PATCH /api/customer-payments/:uuid
export async function updateCustomerPayment(
  uuid: string,
  updateDto: UpdateCustomerPaymentDto
): Promise<CustomerPayment> {
  try {
    const response = await axios.patch(`${API_BASE}/customer-payments/${uuid}`, updateDto);
    return response.data;
  } catch (error: any) {
    console.error('Update customer payment error:', error.response?.data || error.message);
    throw error;
  }
}

// Refund a completed payment
// POST /api/customer-payments/:uuid/refund
export async function refundCustomerPayment(
  uuid: string,
  userUuid: string,
  reason: string
): Promise<RefundResponse> {
  try {
    const response = await axios.post(`${API_BASE}/customer-payments/${uuid}/refund`, {
      userUuid,
      reason
    });
    return response.data;
  } catch (error: any) {
    console.error('Refund customer payment error:', error.response?.data || error.message);
    throw error;
  }
}

// Cancel a pending payment
// POST /api/customer-payments/:uuid/cancel
export async function cancelCustomerPayment(
  uuid: string,
  userUuid: string
): Promise<CustomerPayment> {
  try {
    const response = await axios.post(`${API_BASE}/customer-payments/${uuid}/cancel`, {
      userUuid
    });
    return response.data;
  } catch (error: any) {
    console.error('Cancel customer payment error:', error.response?.data || error.message);
    throw error;
  }
}

// Delete a customer payment (soft delete - only pending payments)
// DELETE /api/customer-payments/:uuid
export async function deleteCustomerPayment(uuid: string): Promise<void> {
  try {
    await axios.delete(`${API_BASE}/customer-payments/${uuid}`);
  } catch (error: any) {
    console.error('Delete customer payment error:', error.response?.data || error.message);
    throw error;
  }
}

// Helper functions



// Format payment method for display
export function formatPaymentMethod(method: PaymentMethod): string {
  const methodLabels: Record<PaymentMethod, string> = {
    cash: 'Cash',
    credit_card: 'Credit Card',
    debit_card: 'Debit Card',
    bank_transfer: 'Bank Transfer',
    check: 'Check',
    mobile_payment: 'Mobile Payment'
  };
  return methodLabels[method] || method;
}

// Format payment status for display
export function formatPaymentStatus(status: PaymentStatus): string {
  const statusLabels: Record<PaymentStatus, string> = {
    pending: 'Pending',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    refunded: 'Refunded'
  };
  return statusLabels[status] || status;
}

// Get status color for display
export function getStatusColor(status: PaymentStatus): string {
  const statusColors: Record<PaymentStatus, string> = {
    pending: 'text-yellow-600 bg-yellow-100',
    completed: 'text-green-600 bg-green-100',
    failed: 'text-red-600 bg-red-100',
    cancelled: 'text-gray-600 bg-gray-100',
    refunded: 'text-blue-600 bg-blue-100'
  };
  return statusColors[status] || 'text-gray-600 bg-gray-100';
}

// Customer-related endpoints from customer controller

// Get customer payment history using customer controller endpoint
// GET /api/customers/:uuid/payments
export async function getCustomerPaymentHistory(
  customerUuid: string,
  page: number = 1,
  limit: number = 10
): Promise<PaginatedResponseDto<CustomerPayment>> {
  try {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    const url = `${API_BASE}/customers/${customerUuid}/payments?${params.toString()}`;

    const response = await axios.get(url);
    return response.data;
  } catch (error: any) {
    console.error('Get customer payment history error:', error.response?.data || error.message);
    throw error;
  }
}

// Get customer credit balance from customer controller
// GET /api/customers/:uuid/credit
export async function getCustomerCredit(customerUuid: string): Promise<{ currentCredit: number }> {
  try {
    const response = await axios.get(`${API_BASE}/customers/${customerUuid}/credit`);
    return response.data;
  } catch (error: any) {
    console.error('Get customer credit error:', error.response?.data || error.message);
    throw error;
  }
}

// Get customer sales history for context
// GET /api/customers/:uuid/sales
export async function getCustomerSales(
  customerUuid: string,
  page: number = 1,
  limit: number = 10
): Promise<{
  data: CustomerSale[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());

    const url = `${API_BASE}/customers/${customerUuid}/sales?${params.toString()}`;

    const response = await axios.get(url);
    return response.data;
  } catch (error: any) {
    console.error('Get customer sales error:', error.response?.data || error.message);
    throw error;
  }
}

// Get customer details with credit info
// GET /api/customers/:uuid
export async function getCustomerWithCredit(customerUuid: string): Promise<Customer> {
  try {
    const response = await axios.get(`${API_BASE}/customers/${customerUuid}`);
    return response.data;
  } catch (error: any) {
    console.error('Get customer with credit error:', error.response?.data || error.message);
    throw error;
  }
}



// Format customer type for display
export function formatCustomerType(type: string): string {
  const typeLabels: Record<string, string> = {
    retail: 'Retail',
    wholesale: 'Wholesale',
    'mid-wholesale': 'Mid-Wholesale',
    institutional: 'Institutional'
  };
  return typeLabels[type] || type;
} 