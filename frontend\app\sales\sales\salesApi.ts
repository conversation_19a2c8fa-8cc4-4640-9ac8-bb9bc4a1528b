// salesApi.ts - API utilities for Sales endpoints
import axios from 'axios';
import { getAxiosAuthHeaders } from '@/utils/authHeaders';

const API_BASE = '/api/sales';

export interface SaleItemSnapshot {
  productUuid: string;
  name: string;
  quantity: number;
  unitPrice: number;
  lineTotal: number;
  taxAmount: number;
  productUuidString: string;
}

export interface Sale {
  uuid: string;
  invoiceNumber: string;
  customerUuid: string;
  customerUuidString: string;
  customerName?: string;
  customerFiscalId?: string;
  customerRc?: string;
  customerArticleNumber?: string;
  orderUuid?: string;
  orderUuidString?: string;
  itemsSnapshot: SaleItemSnapshot[];
  subtotal: number;
  useTax: boolean;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
  amountPaid: number;
  balanceDue: number;
  paymentMethod: string;
  // Removed paymentDate since it's redundant with updatedAt and excluded from logs
  invoiceDate: string;
  dueDate: string;
  status: 'paid' | 'partially_paid' | 'unpaid' | 'cancelled';
  createdBy: string;
  createdByString: string;
  updatedBy: string;
  updatedByString: string;
  createdAt: string;
  updatedAt: string;
}

export interface SaleItem {
  uuid: string;
  saleUuid: string;
  productUuid: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface CreateSaleDto {
  customerUuid: string;
  warehouseUuid: string;
  userUuid: string;
  items: Array<{
    productUuid: string;
    quantity: number;
    unitPrice: number;
  }>;
  paymentMethod: string;
  amountPaid: number;
  useTax: boolean;
  taxRate: number;
}

export interface UpdateSaleDto {
  userUuid: string;
  customerUuid?: string;
  paymentMethod?: string;
  amountPaid?: number;
  useTax?: boolean;
  taxRate?: number;
}

// Updated to match backend enum values
export enum SaleStatus {
  PAID = "paid",
  PARTIALLY_PAID = "partially_paid",
  UNPAID = "unpaid",
  CANCELLED = "cancelled",
}

// Updated to match backend enum values
export enum PaymentMethods {
  CASH = "cash",
  CREDIT_CARD = "credit_card",
  BANK_TRANSFER = "bank_transfer",
  MOBILE_PAYMENT = "mobile_payment",
  CHEQUE = "cheque",
  OTHER = "other",
}

export interface FilterSaleDto {
  warehouseUuid?: string;
  customerUuid?: string;
  customerName?: string; // Keep for backward compatibility
  status?: SaleStatus;
  paymentMethod?: PaymentMethods;
  invoiceNumber?: string;
  // Creation date range (createdFrom/createdTo)
  createdFrom?: string;
  createdTo?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface PaginationQueryDto {
  page?: number;
  limit?: number;
}

export interface SalesResponse {
  data: Sale[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
  totalPages: number;
}

/**
 * Get all sales with optional filtering and pagination
 */
export async function getSales(pagination?: PaginationQueryDto, filter?: FilterSaleDto): Promise<SalesResponse> {
  const params = new URLSearchParams();
  
  // Add pagination parameters with validation
  if (pagination?.page && !isNaN(pagination.page) && pagination.page > 0) {
    params.append('page', pagination.page.toString());
  }
  if (pagination?.limit && !isNaN(pagination.limit) && pagination.limit > 0 && pagination.limit <= 100) {
    params.append('limit', pagination.limit.toString());
  }
  
  // Helper function to validate and add string parameters
  const addStringParam = (key: string, value: string | undefined) => {
    if (value && typeof value === 'string' && value.trim() !== '' && value.trim() !== 'undefined' && value.trim() !== 'null') {
      params.append(key, value.trim());
    }
  };
  
  // Add filter parameters with validation
  addStringParam('warehouseUuid', filter?.warehouseUuid);
  addStringParam('customerUuid', filter?.customerUuid);
  addStringParam('customerName', filter?.customerName);
  addStringParam('status', filter?.status);
  addStringParam('paymentMethod', filter?.paymentMethod);
  addStringParam('invoiceNumber', filter?.invoiceNumber);
  addStringParam('createdFrom', filter?.createdFrom);
  addStringParam('createdTo', filter?.createdTo);
  
  // Validate and add numeric filters - only send valid numbers
  if (filter?.minAmount !== undefined && filter.minAmount !== null && !isNaN(filter.minAmount) && filter.minAmount >= 0) {
    params.append('minAmount', filter.minAmount.toString());
  }
  if (filter?.maxAmount !== undefined && filter.maxAmount !== null && !isNaN(filter.maxAmount) && filter.maxAmount >= 0) {
    params.append('maxAmount', filter.maxAmount.toString());
  }

  const url = `${API_BASE}?${params.toString()}`;
  const headers = getAxiosAuthHeaders();
  
  try {
    const res = await axios.get(url, {
      headers,
    });
    return res.data;
  } catch (error: any) {
    throw error;
  }
}

/**
 * Get a single sale by UUID
 */
export async function getSale(uuid: string): Promise<Sale> {
  const res = await axios.get(`${API_BASE}/${uuid}`, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Create a new sale
 */
export async function createSale(data: CreateSaleDto): Promise<Sale> {
  const res = await axios.post(API_BASE, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Update sale details
 */
export async function updateSale(uuid: string, data: UpdateSaleDto): Promise<Sale> {
  const res = await axios.patch(`${API_BASE}/${uuid}`, data, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

/**
 * Cancel a sale
 */
export async function cancelSale(uuid: string, userUuid: string): Promise<Sale> {
  const res = await axios.patch(`${API_BASE}/${uuid}/cancel`, { userUuid }, {
    headers: getAxiosAuthHeaders(),
  });
  return res.data;
}

 