import {
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsNumber,
  IsEnum,
  IsDateString,
} from "class-validator";
import { Type } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";
import { QuoteStatus } from "../quote.entity";

class QuoteItemSnapshotDto {
  @ApiProperty({ example: "uuid7_prod1" })
  @IsString()
  @IsNotEmpty()
  productUuid: string; // No change, this is a reference

  @ApiProperty({ example: "Product Name" })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ example: 10 })
  @IsNumber()
  quantity: number;

  @ApiProperty({ example: 25.5 })
  @IsNumber()
  unitPrice: number;

  @ApiProperty({ example: 255 })
  @IsNumber()
  lineTotal: number;
}

export class CreateQuoteDto {
  @ApiProperty({ example: "QU-2025-0001" })
  @IsString()
  @IsNotEmpty()
  quoteNumber: string;

  @ApiProperty({ example: "uuid7_customer" })
  @IsString()
  @IsNotEmpty()
  customerUuid: string; // No change, this is a reference

  @ApiProperty({ type: [QuoteItemSnapshotDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuoteItemSnapshotDto)
  itemsSnapshot: QuoteItemSnapshotDto[];

  @ApiProperty({ example: 255 })
  @IsNumber()
  totalAmount: number;

  @ApiProperty({ enum: QuoteStatus, example: QuoteStatus.DRAFT })
  @IsEnum(QuoteStatus)
  status: QuoteStatus;

  @ApiProperty({ example: "2025-06-16T12:00:00.000Z" })
  @IsDateString()
  quoteDate: string;

  @ApiProperty({ example: "2025-07-16T12:00:00.000Z" })
  @IsDateString()
  expiryDate: string;

  @ApiProperty({ example: "uuid7_user" })
  @IsString()
  @IsNotEmpty()
  createdBy: string;

  @ApiProperty({ example: "uuid7_user" })
  @IsString()
  @IsNotEmpty()
  updatedBy: string;
}
