import { En<PERSON>ty, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { Quote } from './quote.entity';

@Entity('quote_items')
export class QuoteItem {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the quote item (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the quote this item belongs to",
  })
  @Column('uuid')
  @Index()
  quoteUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @Column('uuid')
  @Index()
  productUuid: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of quote",
  })
  @Column()
  name: string;

  @ApiProperty({
    example: 5,
    description: "Quantity quoted",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  quantity: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of quote",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  unitPrice: number;

  @ApiProperty({
    example: 129.95,
    description: "Line total (quantity * unit price)",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  lineTotal: number;

  @ApiProperty({
    example: "Special notes for this item",
    description: "Item-specific notes",
    required: false,
  })
  @Column({ nullable: true })
  notes?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to Quote
  @ManyToOne(() => Quote, quote => quote.quoteItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'quoteUuid' })
  quote: Quote;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 