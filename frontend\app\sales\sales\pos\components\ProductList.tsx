import React, { useEffect, useState } from 'react';
import { posStyles } from '../styles/posStyles';
import { formatCurrency, getStockStatus } from '../utils/posHelpers';
import type { ProductListProps } from '../types';
import '../../styles/ProductList.fadein.css'; // Import the fade-in CSS

export function ProductList({
  products,
  selectedIndex,
  searchTerm,
  cartItems = [],
  onProductSelect,
  isLoading = false,
  disabled = false,
}: ProductListProps) {
  const [showProductList, setShowProductList] = useState(false);
  const [dataKey, setDataKey] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    // Start animating state and show product list with entrance animation
    setIsAnimating(true);
    setShowProductList(true);
    
    // Remove animating state after entrance animation completes
    const timer = setTimeout(() => {
      setIsTransitioning(true);
      // Gradual transition out of animation state
      setTimeout(() => {
        setIsAnimating(false);
        setIsTransitioning(false);
      }, 200);
    }, 1500); // Extended duration to prevent premature scrollbar appearance
    
    return () => clearTimeout(timer);
  }, []);

  // Reset animation when products change
  useEffect(() => {
    if (products.length > 0) {
      setIsAnimating(true);
      setDataKey(prev => prev + 1);
      
      // Remove animating state after staggered animations complete
      const timer = setTimeout(() => {
        setIsTransitioning(true);
        // Gradual transition out of animation state
        setTimeout(() => {
          setIsAnimating(false);
          setIsTransitioning(false);
        }, 200);
      }, 1800); // Extended time for all staggered animations to complete
      
      return () => clearTimeout(timer);
    }
  }, [products.length]);

  // Determine container classes based on animation state
  const containerClasses = `product-list-container ${showProductList ? 'product-list-entrance' : ''} ${
    isAnimating || isTransitioning ? 'animating' : ''
  }`;

  return (
    <div 
      className="h-full flex flex-col product-list-fade-in" 
      style={{
        scrollbarWidth: 'thin',
        scrollbarColor: '#9ca3af #f3f4f6'
      }}
    >
      {/* Debug: Show product count */}
      {process.env.NODE_ENV === 'development' && (
        <div className="text-xs text-gray-500 p-2 bg-blue-50 border-b flex-shrink-0">
          Showing {products.length} products on this page | Container: 100% height of parent
        </div>
      )}
      <style jsx>{`
        .product-list-scroll::-webkit-scrollbar {
          width: 8px;
        }
        .product-list-scroll::-webkit-scrollbar-track {
          background: #f3f4f6;
          border-radius: 4px;
        }
        .product-list-scroll::-webkit-scrollbar-thumb {
          background: #9ca3af;
          border-radius: 4px;
        }
        .product-list-scroll::-webkit-scrollbar-thumb:hover {
          background: #6b7280;
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        .product-list-container {
          height: 100%;
          overflow-y: auto;
          overflow-x: hidden;
        }
      `}</style>
      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center py-12 text-gray-500">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
            <div className={posStyles.empty?.text || "text-lg font-medium text-gray-600"}>Loading products...</div>
            <div className={posStyles.empty?.subtext || "text-sm text-gray-500 mt-2"}>
              Please wait while we fetch the latest products
            </div>
          </div>
        </div>
      ) : products.length === 0 ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center py-12 text-gray-500">
            {searchTerm ? (
              <>
                <div className={posStyles.empty?.text || "text-lg font-medium text-gray-600"}>No products found</div>
                <div className={posStyles.empty?.subtext || "text-sm text-gray-500 mt-2"}>
                  Try adjusting your search terms or clear the search
                </div>
              </>
            ) : (
              <>
                <div className={posStyles.empty?.text || "text-lg font-medium text-gray-600"}>No products found</div>
                <div className={posStyles.empty?.subtext || "text-sm text-gray-500 mt-2"}>
                  No products match your current filters. Try clearing filters or check back later.
                </div>
              </>
            )}
          </div>
        </div>
      ) : (
        <div className={`product-list-container product-list-scroll ${containerClasses}`}>
          <div className="space-y-1 p-1">
            {products.map((product, index) => {
            const stockStatus = getStockStatus(product);
            const isSelected = index === selectedIndex;
            // Use currentStock from main storage if available, fallback to stockQuantity
            const stock = product.currentStock !== undefined ? product.currentStock : (product.stockQuantity || 0);
            const isOutOfStock = stock === 0;
            const hasNegativeStock = stock < 0;
            
            // Use customer price if available, otherwise fall back to regular price
            const displayPrice = product.customerPrice || product.price;
            const customerType = product.customerType;
            
            // Check if product is in cart and get quantity
            const cartItem = cartItems.find(item => item.productUuid === product.uuid);
            const cartQuantity = cartItem ? cartItem.quantity : 0;
            const isInCart = cartQuantity > 0;
            
            return (
              <div
                key={`${dataKey}-${product.uuid}`}
                className={`product-item ${posStyles.cartQuantity.productWrapper}`}
              >
                {/* Cart quantity badge */}
                {isInCart && (
                  <div className={`${posStyles.cartQuantity.badge} cart-quantity-badge`}>
                    {cartQuantity > 99 ? '99+' : Math.round(cartQuantity)}
                  </div>
                )}
                
                <div
                  className={`${
                    isSelected 
                      ? 'bg-blue-50 border-blue-500 border-2 selected'
                      : isInCart
                        ? posStyles.cartQuantity.highlight
                        : 'bg-white border-gray-200 border hover:bg-gray-50 hover:border-blue-300'
                  } ${isOutOfStock ? 'opacity-60' : ''} ${(isLoading || disabled) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} p-2 rounded-lg transition-colors`}
                  onClick={() => !isLoading && !disabled && onProductSelect(product, index)}
                >
                {/* Changed from flex with justify-between to a more controlled grid layout */}
                <div className="grid grid-cols-12 gap-3 items-start">
                  {/* Product info section - takes up 8 columns */}
                  <div className="col-span-8 min-w-0">
                    <div className="font-medium text-sm text-gray-900 truncate">
                      {product.name}
                    </div>
                    
                    {/* Product meta info - use flex-wrap to prevent overflow */}
                    <div className="flex flex-wrap items-center gap-2 mt-0.5">
                      {product.sku && (
                        <span className="text-xs text-gray-500 truncate">
                          SKU: {product.sku}
                        </span>
                      )}
                      
                      {product.sku && product.barcode && (
                        <span className="text-xs text-gray-300">•</span>
                      )}
                      
                      {product.barcode && (
                        <span className="text-xs text-gray-500 truncate">
                          Barcode: {product.barcode}
                        </span>
                      )}
                      
                      {(product.sku || product.barcode) && product.category && (
                        <span className="text-xs text-gray-300">•</span>
                      )}
                      
                      {product.category && (
                        <span className="text-xs text-gray-500 truncate">
                          {product.category}
                        </span>
                      )}
                      
                      {(product.sku || product.barcode || product.category) && customerType && (
                        <span className="text-xs text-gray-300">•</span>
                      )}
                      
                      {customerType && (
                        <span className="text-xs px-1 py-0.5 bg-blue-100 text-blue-800 rounded-full whitespace-nowrap">
                          {customerType.charAt(0).toUpperCase() + customerType.slice(1)} Price
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* Price and stock section - takes up 4 columns with right alignment */}
                  <div className="col-span-4 text-right min-w-0">
                    {displayPrice && (
                      <div className="text-sm font-bold text-green-600 truncate">
                        {formatCurrency(displayPrice)}
                      </div>
                    )}
                    
                    {/* Show original retail price if different from customer price */}
                    {product.customerPrice && product.retailPrice && product.customerPrice !== product.retailPrice && (
                      <div className="text-xs text-gray-500 line-through truncate">
                        {formatCurrency(product.retailPrice)}
                      </div>
                    )}
                    
                    <div className={`text-xs ${stockStatus.color} mt-0.5 flex items-center justify-end gap-1`}>
                      {hasNegativeStock && (
                        <span className="text-red-600 font-bold">⚠️</span>
                      )}
                      <span className="truncate">{stockStatus.text}</span>
                    </div>
                  </div>
                </div>
                </div>
              </div>
            );
          })}
          </div>
        </div>
      )}
    </div>
  );
} 