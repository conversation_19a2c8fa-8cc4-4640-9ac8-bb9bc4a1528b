import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, ILike, In } from "typeorm";
import { UsersService } from "../users/users.service";
import { PurchaseReturn, PurchaseReturnStatus } from "./purchase-return.entity";
import { PurchaseReturnItem } from "./purchase-return-item.entity";
import { Purchase, PurchaseStatus } from "./purchase.entity";
import { PurchaseItem } from "./purchase-item.entity";
import { Supplier } from "../suppliers/supplier.entity";
import { Product } from "../products/product.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { 
  EMPTY_STRING_FILTER, 
  EMPTY_UUID_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
  RETURN_REASONS,
} from "./purchase.constants";
import {
  PurchaseReturnResponseDto,
  toPurchaseReturnResponseDto,
  toPurchaseReturnResponseDtoArray,
} from "./dto/purchase-return.dto";
import { InventoryService } from "../inventory/inventory.service.typeorm";
import { StockAdjustmentService } from "../inventory/stock-adjustment.service";

@Injectable()
export class PurchaseReturnService {
  constructor(
    @InjectRepository(PurchaseReturn)
    private purchaseReturnRepository: Repository<PurchaseReturn>,
    @InjectRepository(PurchaseReturnItem)
    private purchaseReturnItemRepository: Repository<PurchaseReturnItem>,
    @InjectRepository(Purchase)
    private purchaseRepository: Repository<Purchase>,
    @InjectRepository(PurchaseItem)
    private purchaseItemRepository: Repository<PurchaseItem>,
    @InjectRepository(Supplier)
    private supplierRepository: Repository<Supplier>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Warehouse)
    private warehouseRepository: Repository<Warehouse>,
    private usersService: UsersService,
    private inventoryService: InventoryService,
    private stockAdjustmentService: StockAdjustmentService,
  ) {}

  /**
   * Utility function to round numbers to 2 decimal places
   */
  private roundToTwoDecimals(value: number): number {
    return Math.round(value * 100) / 100;
  }

  /**
   * Generate a unique return number
   */
  private async generateReturnNumber(): Promise<string> {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const prefix = "PR";
    
    // Find the latest return number for today
    const latestReturn = await this.purchaseReturnRepository.findOne({
      where: {
        returnNumber: ILike(`${prefix}-${dateStr}-%`),
      },
      order: { returnNumber: 'DESC' },
    });

    let sequence = 1;
    if (latestReturn) {
      const lastSequence = parseInt(latestReturn.returnNumber.split('-')[2]);
      sequence = lastSequence + 1;
    }

    return `${prefix}-${dateStr}-${sequence.toString().padStart(3, '0')}`;
  }

  /**
   * Create a new purchase return
   */
  async create(
    userUuid: string,
    purchaseUuid: string,
    returnItems: any[],
    returnReason: string,
    returnDate: Date,
    expectedReturnDate?: Date,
    returnDescription?: string,
  ): Promise<PurchaseReturnResponseDto> {
    // Validate required fields
    if (!userUuid || !purchaseUuid || !returnItems || returnItems.length === 0) {
      throw new BadRequestException(
        "userUuid, purchaseUuid, and returnItems are required to create a purchase return",
      );
    }

    // Check user exists
    const user = await this.usersService.findOne(userUuid);
    if (!user) throw new NotFoundException("User not found");

    // Check purchase exists and is in a valid state for return
    const purchase = await this.purchaseRepository.findOne({
      where: { id: purchaseUuid, isDeleted: false },
      relations: ['purchaseItems'],
    });
    if (!purchase) throw new NotFoundException("Purchase not found");

    if (![PurchaseStatus.RECEIVED, PurchaseStatus.PARTIALLY_PAID, PurchaseStatus.PAID].includes(purchase.status)) {
      throw new BadRequestException("Purchase must be received, partially paid, or paid to create a return");
    }

    // Validate return reason
    const validReasons = Object.values(RETURN_REASONS);
    if (!validReasons.includes(returnReason as any)) {
      throw new BadRequestException("Invalid return reason");
    }

    // Validate return items
    const validatedItems = await this.validateReturnItems(returnItems, purchase.purchaseItems);

    // Calculate total return amount
    const totalReturnAmount = this.roundToTwoDecimals(
      validatedItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );

    // Generate return number
    const returnNumber = await this.generateReturnNumber();

    // Create purchase return
    const purchaseReturn = this.purchaseReturnRepository.create({
      id: PurchaseReturn.generateId(),
      returnNumber,
      purchaseUuid,
      supplierUuid: purchase.supplierUuid,
      supplierName: purchase.supplierName,
      warehouseUuid: purchase.warehouseUuid,
      warehouseName: purchase.warehouseName,
      returnReason,
      returnDate,
      expectedReturnDate,
      returnDescription,
      totalReturnAmount,
      status: PurchaseReturnStatus.PENDING,
      createdBy: userUuid,
      updatedBy: userUuid,
    });

    const savedReturn = await this.purchaseReturnRepository.save(purchaseReturn);

    // Create return items
    const returnItemEntities = validatedItems.map(item => 
      this.purchaseReturnItemRepository.create({
        id: PurchaseReturnItem.generateId(),
        purchaseReturnUuid: savedReturn.id,
        purchaseItemUuid: item.purchaseItemUuid,
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
        itemReturnReason: item.itemReturnReason,
        createdBy: userUuid,
        updatedBy: userUuid,
      })
    );

    await this.purchaseReturnItemRepository.save(returnItemEntities);

    // Update purchase status to returned
    await this.purchaseRepository.update(purchaseUuid, {
      status: PurchaseStatus.RETURNED,
      updatedBy: userUuid,
    });

    return this.findOne(savedReturn.id);
  }

  /**
   * Validate return items against purchase items
   */
  private async validateReturnItems(returnItems: any[], purchaseItems: PurchaseItem[]): Promise<any[]> {
    const validatedItems = [];

    for (const returnItem of returnItems) {
      const purchaseItem = purchaseItems.find(pi => pi.id === returnItem.purchaseItemUuid);
      if (!purchaseItem) {
        throw new BadRequestException(`Purchase item ${returnItem.purchaseItemUuid} not found`);
      }

      if (returnItem.quantity > purchaseItem.quantity) {
        throw new BadRequestException(`Return quantity cannot exceed purchased quantity for item ${purchaseItem.name}`);
      }

      const lineTotal = this.roundToTwoDecimals(returnItem.quantity * returnItem.unitPrice);

      validatedItems.push({
        ...returnItem,
        productUuid: purchaseItem.productUuid,
        name: purchaseItem.name,
        lineTotal,
      });
    }

    return validatedItems;
  }

  /**
   * Find all purchase returns with filtering
   */
  async findAll({
    purchaseUuid = EMPTY_UUID_FILTER,
    supplierUuid = EMPTY_UUID_FILTER,
    warehouseUuid = EMPTY_UUID_FILTER,
    status = EMPTY_STRING_FILTER,
    returnReason = EMPTY_STRING_FILTER,
    createdFrom,
    createdTo,
    page = 1,
    limit = 10,
    returnNumber,
    minAmount = MIN_NUMBER_FILTER,
    maxAmount = MAX_NUMBER_FILTER,
  }: {
    purchaseUuid?: string;
    supplierUuid?: string;
    warehouseUuid?: string;
    status?: string;
    returnReason?: string;
    createdFrom?: Date;
    createdTo?: Date;
    page?: number;
    limit?: number;
    returnNumber?: string;
    minAmount?: number;
    maxAmount?: number;
  } = {}): Promise<{ returns: PurchaseReturnResponseDto[]; total: number; page: number; limit: number; totalPages: number }> {
    const queryBuilder = this.purchaseReturnRepository
      .createQueryBuilder('pr')
      .leftJoinAndSelect('pr.returnItems', 'ri')
      .where('pr.isDeleted = :isDeleted', { isDeleted: false });

    // Apply filters
    if (purchaseUuid && purchaseUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere('pr.purchaseUuid = :purchaseUuid', { purchaseUuid });
    }

    if (supplierUuid && supplierUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere('pr.supplierUuid = :supplierUuid', { supplierUuid });
    }

    if (warehouseUuid && warehouseUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere('pr.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    if (status && status !== EMPTY_STRING_FILTER) {
      queryBuilder.andWhere('pr.status = :status', { status });
    }

    if (returnReason && returnReason !== EMPTY_STRING_FILTER) {
      queryBuilder.andWhere('pr.returnReason = :returnReason', { returnReason });
    }

    if (returnNumber) {
      queryBuilder.andWhere('pr.returnNumber ILIKE :returnNumber', { returnNumber: `%${returnNumber}%` });
    }

    if (createdFrom) {
      queryBuilder.andWhere('pr.createdAt >= :createdFrom', { createdFrom });
    }

    if (createdTo) {
      queryBuilder.andWhere('pr.createdAt <= :createdTo', { createdTo });
    }

    if (minAmount !== MIN_NUMBER_FILTER) {
      queryBuilder.andWhere('pr.totalReturnAmount >= :minAmount', { minAmount });
    }

    if (maxAmount !== MAX_NUMBER_FILTER) {
      queryBuilder.andWhere('pr.totalReturnAmount <= :maxAmount', { maxAmount });
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit).orderBy('pr.createdAt', 'DESC');

    const returns = await queryBuilder.getMany();

    return {
      returns: toPurchaseReturnResponseDtoArray(returns),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Find a single purchase return by ID
   */
  async findOne(uuid: string): Promise<PurchaseReturnResponseDto> {
    const purchaseReturn = await this.purchaseReturnRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['returnItems'],
    });

    if (!purchaseReturn) {
      throw new NotFoundException("Purchase return not found");
    }

    return toPurchaseReturnResponseDto(purchaseReturn);
  }

  /**
   * Approve a purchase return
   */
  async approveReturn(uuid: string, userUuid: string): Promise<PurchaseReturnResponseDto> {
    const purchaseReturn = await this.purchaseReturnRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!purchaseReturn) {
      throw new NotFoundException("Purchase return not found");
    }

    if (purchaseReturn.status !== PurchaseReturnStatus.PENDING) {
      throw new BadRequestException("Only pending returns can be approved");
    }

    await this.purchaseReturnRepository.update(uuid, {
      status: PurchaseReturnStatus.APPROVED,
      approvedAt: new Date(),
      approvedBy: userUuid,
      updatedBy: userUuid,
    });

    return this.findOne(uuid);
  }

  /**
   * Mark return as shipped
   */
  async shipReturn(uuid: string, userUuid: string, trackingNumber?: string, shippingMethod?: string): Promise<PurchaseReturnResponseDto> {
    const purchaseReturn = await this.purchaseReturnRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!purchaseReturn) {
      throw new NotFoundException("Purchase return not found");
    }

    if (purchaseReturn.status !== PurchaseReturnStatus.APPROVED) {
      throw new BadRequestException("Only approved returns can be shipped");
    }

    await this.purchaseReturnRepository.update(uuid, {
      status: PurchaseReturnStatus.SHIPPED,
      shippedAt: new Date(),
      shippedBy: userUuid,
      trackingNumber,
      shippingMethod,
      updatedBy: userUuid,
    });

    return this.findOne(uuid);
  }

  /**
   * Mark return as received by supplier
   */
  async markReceivedBySupplier(uuid: string, userUuid: string): Promise<PurchaseReturnResponseDto> {
    const purchaseReturn = await this.purchaseReturnRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!purchaseReturn) {
      throw new NotFoundException("Purchase return not found");
    }

    if (purchaseReturn.status !== PurchaseReturnStatus.SHIPPED) {
      throw new BadRequestException("Only shipped returns can be marked as received");
    }

    await this.purchaseReturnRepository.update(uuid, {
      status: PurchaseReturnStatus.RECEIVED_BY_SUPPLIER,
      receivedBySupplierAt: new Date(),
      updatedBy: userUuid,
    });

    return this.findOne(uuid);
  }

  /**
   * Process refund for return
   */
  async processRefund(uuid: string, userUuid: string): Promise<PurchaseReturnResponseDto> {
    const purchaseReturn = await this.purchaseReturnRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['returnItems'],
    });

    if (!purchaseReturn) {
      throw new NotFoundException("Purchase return not found");
    }

    if (purchaseReturn.status !== PurchaseReturnStatus.RECEIVED_BY_SUPPLIER) {
      throw new BadRequestException("Only returns received by supplier can be refunded");
    }

    // Process inventory adjustments for returned items
    await this.processInventoryAdjustments(purchaseReturn, userUuid);

    await this.purchaseReturnRepository.update(uuid, {
      status: PurchaseReturnStatus.REFUNDED,
      refundedAt: new Date(),
      refundedBy: userUuid,
      updatedBy: userUuid,
    });

    return this.findOne(uuid);
  }

  /**
   * Process inventory adjustments for returned items
   */
  private async processInventoryAdjustments(purchaseReturn: PurchaseReturn, userUuid: string): Promise<void> {
    for (const returnItem of purchaseReturn.returnItems) {
      // Reduce inventory for returned items
      await this.stockAdjustmentService.createStockAdjustment({
        productUuid: returnItem.productUuid,
        warehouseUuid: purchaseReturn.warehouseUuid,
        storageUuid: returnItem.storageUuid || purchaseReturn.warehouseUuid, // Use warehouse as fallback
        userUuid: userUuid,
        quantityAdjusted: -returnItem.quantity, // Negative to reduce stock
        reason: `Purchase return: ${purchaseReturn.returnNumber}`,
      });
    }
  }

  /**
   * Cancel a purchase return
   */
  async cancelReturn(uuid: string, userUuid: string): Promise<PurchaseReturnResponseDto> {
    const purchaseReturn = await this.purchaseReturnRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!purchaseReturn) {
      throw new NotFoundException("Purchase return not found");
    }

    if (purchaseReturn.status === PurchaseReturnStatus.REFUNDED) {
      throw new BadRequestException("Refunded returns cannot be cancelled");
    }

    await this.purchaseReturnRepository.update(uuid, {
      status: PurchaseReturnStatus.CANCELLED,
      updatedBy: userUuid,
    });

    return this.findOne(uuid);
  }

  /**
   * Get return analytics
   */
  async getReturnAnalytics(dateRange?: { startDate: Date; endDate: Date }) {
    const queryBuilder = this.purchaseReturnRepository
      .createQueryBuilder('pr')
      .where('pr.isDeleted = :isDeleted', { isDeleted: false });

    if (dateRange) {
      queryBuilder.andWhere('pr.createdAt >= :startDate', { startDate: dateRange.startDate });
      queryBuilder.andWhere('pr.createdAt <= :endDate', { endDate: dateRange.endDate });
    }

    const returns = await queryBuilder.getMany();

    const analytics = {
      totalReturns: returns.length,
      totalReturnAmount: returns.reduce((sum, r) => sum + r.totalReturnAmount, 0),
      averageReturnAmount: returns.length > 0 ? returns.reduce((sum, r) => sum + r.totalReturnAmount, 0) / returns.length : 0,
      returnsByStatus: {},
      returnsByReason: {},
      returnsBySupplier: {},
    };

    // Group by status
    returns.forEach(r => {
      analytics.returnsByStatus[r.status] = (analytics.returnsByStatus[r.status] || 0) + 1;
    });

    // Group by reason
    returns.forEach(r => {
      analytics.returnsByReason[r.returnReason] = (analytics.returnsByReason[r.returnReason] || 0) + 1;
    });

    // Group by supplier
    returns.forEach(r => {
      if (r.supplierUuid) {
        analytics.returnsBySupplier[r.supplierUuid] = (analytics.returnsBySupplier[r.supplierUuid] || 0) + 1;
      }
    });

    return analytics;
  }
} 