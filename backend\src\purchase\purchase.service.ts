import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, ILike, In } from "typeorm";
import { UsersService } from "../users/users.service";
import { Purchase, PurchaseStatus, PaymentMethods } from "./purchase.entity";
import { PurchaseItem } from "./purchase-item.entity";
import { Supplier } from "../suppliers/supplier.entity";
import { Product } from "../products/product.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import {
  EMPTY_STRING_FILTER, 
  EMPTY_UUID_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
  PURCHASE_STATUS_FLOW,
} from "./purchase.constants";
import {
  PurchaseResponseDto,
  toPurchaseResponseDto,
  toPurchaseResponseDtoArray,
} from "./dto/purchase-response.dto";
import { InventoryService } from "../inventory/inventory.service.typeorm";

@Injectable()
export class PurchaseService {
  constructor(
    @InjectRepository(Purchase)
    private purchaseRepository: Repository<Purchase>,
    @InjectRepository(PurchaseItem)
    private purchaseItemRepository: Repository<PurchaseItem>,
    @InjectRepository(Supplier)
    private supplierRepository: Repository<Supplier>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Warehouse)
    private warehouseRepository: Repository<Warehouse>,
    private usersService: UsersService,
    private inventoryService: InventoryService,
  ) {}

  /**
   * Utility function to round numbers to 2 decimal places
   * This ensures consistent precision for all monetary calculations
   */
  private roundToTwoDecimals(value: number): number {
    return Math.round(value * 100) / 100;
  }

  /**
   * Create a new Purchase with required validation for supplier and items.
   */
  async create(
    userUuid: string,
    warehouseUuid: string,
    supplierUuid: string | undefined,
    items: any[] | undefined,
    useTax: boolean = false,
    taxRate: number = 0.1,
    status: PurchaseStatus = PurchaseStatus.UNPAID,
    paymentMethod?: PaymentMethods,
    amountPaid?: number,
  ) {
    const now = new Date();

    // Validate required fields
    if (!userUuid || !warehouseUuid) {
      throw new BadRequestException(
        "userUuid and warehouseUuid are required to create a purchase",
      );
    }

    if (!items || items.length === 0) {
      throw new BadRequestException("At least one item is required to create a purchase");
    }

    // Check user exists
    const user = await this.usersService.findOne(userUuid);
    if (!user) throw new NotFoundException("User not found");

    // Check supplier exists if provided
    let supplier: Supplier | null = null;
    if (supplierUuid) {
      supplier = await this.supplierRepository.findOne({
        where: { id: supplierUuid, isDeleted: false }
      });
      if (!supplier) throw new NotFoundException("Supplier not found");
    }

    // Validate items and get product information
    const validatedItems = await this.validateAndTransformItems(items);

    // Calculate totals
    const subtotal = this.roundToTwoDecimals(
      validatedItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );
    const taxAmount = useTax ? this.calculateTaxAmount(subtotal, taxRate) : 0;
    const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);
    const finalAmountPaid = amountPaid || 0;
    const balanceDue = this.roundToTwoDecimals(totalAmount - finalAmountPaid);

    // Generate purchase number
    const purchaseNumber = await this.generatePurchaseNumber();

    // Create purchase
    const purchase = new Purchase();
    purchase.id = Purchase.generateId();
    purchase.purchaseNumber = purchaseNumber;
    purchase.supplierUuid = supplierUuid;
    purchase.supplierName = supplier?.name;
    purchase.supplierFiscalId = supplier?.fiscalId;
    purchase.supplierRc = supplier?.rc;
    purchase.supplierArticleNumber = supplier?.articleNumber;
    purchase.warehouseUuid = warehouseUuid;
    purchase.subtotal = subtotal;
    purchase.useTax = useTax;
    purchase.taxRate = taxRate;
    purchase.taxAmount = taxAmount;
    purchase.totalAmount = totalAmount;
    purchase.amountPaid = finalAmountPaid;
    purchase.balanceDue = balanceDue;
    purchase.paymentMethod = paymentMethod || PaymentMethods.BANK_TRANSFER;
    purchase.purchaseDate = now;
    purchase.dueDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 days default
    purchase.status = status;
    purchase.createdBy = userUuid;
    purchase.updatedBy = userUuid;
    purchase.isDeleted = false;

    const savedPurchase = await this.purchaseRepository.save(purchase);

    // Create purchase items
    const purchaseItems = validatedItems.map((item, index) => {
      const purchaseItem = new PurchaseItem();
      purchaseItem.id = PurchaseItem.generateId();
      purchaseItem.purchaseUuid = savedPurchase.id;
      purchaseItem.productUuid = item.productUuid;
      purchaseItem.name = item.name;
      purchaseItem.quantity = item.quantity;
      purchaseItem.unitPrice = item.unitPrice;
      purchaseItem.lineTotal = item.lineTotal;
      purchaseItem.taxAmount = useTax ? this.calculateTaxAmount(item.lineTotal, taxRate) : 0;
      purchaseItem.order = item.order || index;
      return purchaseItem;
    });

    await this.purchaseItemRepository.save(purchaseItems);

    // Return enriched purchase data
    return await this.findOne(savedPurchase.id);
  }

  /**
   * Calculate tax amount based on subtotal and tax rate
   */
  private calculateTaxAmount(subtotal: number, taxRate: number): number {
    return this.roundToTwoDecimals(subtotal * taxRate);
  }

  /**
   * Generate unique purchase number
   */
  private async generatePurchaseNumber(): Promise<string> {
    const date = new Date();
    const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
    const baseNumber = `PO-${dateStr}`;
    
    const existingPurchases = await this.purchaseRepository.find({
      where: {
        purchaseNumber: ILike(`${baseNumber}%`)
      },
      order: { purchaseNumber: 'DESC' },
      take: 1
    });

    let sequence = 1;
    if (existingPurchases.length > 0) {
      const lastNumber = existingPurchases[0].purchaseNumber;
      const lastSequence = parseInt(lastNumber.split('-')[2]);
      sequence = lastSequence + 1;
    }

    return `${baseNumber}-${sequence.toString().padStart(3, '0')}`;
  }

  /**
   * Find all purchases with filtering and pagination
   */
  async findAll({
    supplierUuid = EMPTY_UUID_FILTER,
    status = EMPTY_STRING_FILTER,
    createdFrom,
    createdTo,
    page = 1,
    limit = 10,
    warehouseUuid,
    purchaseOrderNumber,
    paymentMethod,
    startDate,
    endDate,
    minAmount = MIN_NUMBER_FILTER,
    maxAmount = MAX_NUMBER_FILTER,
  }: {
    supplierUuid?: string;
    status?: string;
    createdFrom?: Date;
    createdTo?: Date;
    page?: number;
    limit?: number;
    warehouseUuid?: string;
    purchaseOrderNumber?: string;
    paymentMethod?: string;
    startDate?: Date;
    endDate?: Date;
    minAmount?: number;
    maxAmount?: number;
  } = {}): Promise<{ purchases: PurchaseResponseDto[]; total: number; page: number; limit: number; totalPages: number }> {
    const queryBuilder = this.purchaseRepository
      .createQueryBuilder('purchase')
      .leftJoinAndSelect('purchase.purchaseItems', 'purchaseItems')
      .where('purchase.isDeleted = :isDeleted', { isDeleted: false });

    // Apply filters
    if (supplierUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere('purchase.supplierUuid = :supplierUuid', { supplierUuid });
    }

    if (status !== EMPTY_STRING_FILTER) {
      queryBuilder.andWhere('purchase.status = :status', { status });
    }

    if (warehouseUuid) {
      // Note: This would need to be implemented based on your warehouse logic
      // For now, we'll skip this filter
    }

    if (purchaseOrderNumber) {
      queryBuilder.andWhere('purchase.purchaseOrderNumber ILIKE :purchaseOrderNumber', { 
        purchaseOrderNumber: `%${purchaseOrderNumber}%` 
      });
    }

    if (paymentMethod) {
      queryBuilder.andWhere('purchase.paymentMethod = :paymentMethod', { paymentMethod });
    }

    if (startDate) {
      queryBuilder.andWhere('purchase.purchaseOrderDate >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('purchase.purchaseOrderDate <= :endDate', { endDate });
    }

    if (createdFrom) {
      queryBuilder.andWhere('purchase.createdAt >= :createdFrom', { createdFrom });
    }

    if (createdTo) {
      queryBuilder.andWhere('purchase.createdAt <= :createdTo', { createdTo });
    }

    if (minAmount !== MIN_NUMBER_FILTER) {
      queryBuilder.andWhere('purchase.totalAmount >= :minAmount', { minAmount });
    }

    if (maxAmount !== MAX_NUMBER_FILTER) {
      queryBuilder.andWhere('purchase.totalAmount <= :maxAmount', { maxAmount });
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy('purchase.createdAt', 'DESC')
      .skip(offset)
      .take(limit);

    const purchases = await queryBuilder.getMany();
    const enrichedPurchases = await this.enrichPurchasesData(purchases);

    return {
      purchases: enrichedPurchases,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Find purchases by warehouse
   */
  async findByWarehouse(warehouseUuid: string): Promise<PurchaseResponseDto[]> {
    const purchases = await this.purchaseRepository.find({
      where: { isDeleted: false },
      relations: ['purchaseItems'],
      order: { createdAt: 'DESC' }
    });

    return await this.enrichPurchasesData(purchases);
  }

  /**
   * Find a single purchase by UUID
   */
  async findOne(uuid: string): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['purchaseItems'],
    });

    if (!purchase) {
      throw new NotFoundException(`Purchase with UUID ${uuid} not found`);
    }

    const enrichedPurchases = await this.enrichPurchasesData([purchase]);
    return enrichedPurchases[0];
  }

  /**
   * Enrich purchase data with additional information
   */
  private async enrichPurchasesData(
    purchases: Purchase[],
  ): Promise<PurchaseResponseDto[]> {
    return purchases.map(purchase => toPurchaseResponseDto(purchase));
  }

  /**
   * Update a purchase
   */
  async update(uuid: string, data: any, userUuid: string): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['purchaseItems'],
    });

    if (!purchase) {
      throw new NotFoundException(`Purchase with UUID ${uuid} not found`);
    }

    // Update basic fields
    if (data.supplierUuid !== undefined) purchase.supplierUuid = data.supplierUuid;
    if (data.supplierName !== undefined) purchase.supplierName = data.supplierName;
    if (data.supplierFiscalId !== undefined) purchase.supplierFiscalId = data.supplierFiscalId;
    if (data.supplierRc !== undefined) purchase.supplierRc = data.supplierRc;
    if (data.supplierArticleNumber !== undefined) purchase.supplierArticleNumber = data.supplierArticleNumber;
    if (data.orderUuid !== undefined) purchase.orderUuid = data.orderUuid;
    if (data.useTax !== undefined) purchase.useTax = data.useTax;
    if (data.taxRate !== undefined) purchase.taxRate = data.taxRate;
    if (data.paymentMethod !== undefined) purchase.paymentMethod = data.paymentMethod;
    if (data.purchaseOrderDate !== undefined) purchase.purchaseDate = new Date(data.purchaseOrderDate);
    if (data.expectedDeliveryDate !== undefined) purchase.expectedDeliveryDate = new Date(data.expectedDeliveryDate);
    if (data.dueDate !== undefined) purchase.dueDate = new Date(data.dueDate);
    if (data.status !== undefined) purchase.status = data.status;
    if (data.amountPaid !== undefined) {
      purchase.amountPaid = data.amountPaid;
      purchase.balanceDue = this.roundToTwoDecimals(purchase.totalAmount - data.amountPaid);
    }

    purchase.updatedBy = userUuid;

    // Update purchase items if provided
    if (data.purchaseItems && Array.isArray(data.purchaseItems)) {
      // Delete existing items
      await this.purchaseItemRepository.delete({ purchaseUuid: uuid });

      // Create new items
      const purchaseItems = data.purchaseItems.map((item: any, index: number) => {
        const purchaseItem = new PurchaseItem();
        purchaseItem.id = PurchaseItem.generateId();
        purchaseItem.purchaseUuid = uuid;
        purchaseItem.productUuid = item.productUuid;
        purchaseItem.name = item.name;
        purchaseItem.quantity = item.quantity;
        purchaseItem.unitPrice = item.unitPrice;
        purchaseItem.lineTotal = this.roundToTwoDecimals(item.quantity * item.unitPrice);
        purchaseItem.taxAmount = purchase.useTax ? this.calculateTaxAmount(purchaseItem.lineTotal, purchase.taxRate) : 0;
        purchaseItem.order = item.order || index;
        return purchaseItem;
      });

      await this.purchaseItemRepository.save(purchaseItems);

      // Recalculate totals
      const subtotal = this.roundToTwoDecimals(
        purchaseItems.reduce((sum, item) => sum + item.lineTotal, 0)
      );
      const taxAmount = purchase.useTax ? this.calculateTaxAmount(subtotal, purchase.taxRate) : 0;
      const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);
      
      purchase.subtotal = subtotal;
      purchase.taxAmount = taxAmount;
      purchase.totalAmount = totalAmount;
      purchase.balanceDue = this.roundToTwoDecimals(totalAmount - purchase.amountPaid);
    }

    await this.purchaseRepository.save(purchase);

    return await this.findOne(uuid);
  }

  /**
   * Update purchase status with validation
   */
  async updatePurchaseStatus(
    uuid: string,
    newStatus: PurchaseStatus,
    userUuid: string,
  ): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!purchase) {
      throw new NotFoundException(`Purchase with UUID ${uuid} not found`);
    }

    // Validate status transition
    if (!this.validateStatusTransition(purchase.status, newStatus)) {
      const allowedTransitions = this.getAllowedStatusTransitions(purchase.status);
      throw new BadRequestException(
        `Invalid status transition from ${purchase.status} to ${newStatus}. Allowed transitions: ${allowedTransitions.join(', ')}`
      );
    }

    // Handle special status transitions
    if (newStatus === PurchaseStatus.RECEIVED && !purchase.actualDeliveryDate) {
      purchase.actualDeliveryDate = new Date();
    }

    purchase.status = newStatus;
    purchase.updatedBy = userUuid;

    await this.purchaseRepository.save(purchase);

    return await this.findOne(uuid);
  }

  /**
   * Remove a purchase (soft delete)
   */
  async remove(uuid: string): Promise<{ message: string }> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!purchase) {
      throw new NotFoundException(`Purchase with UUID ${uuid} not found`);
    }

    purchase.isDeleted = true;
    await this.purchaseRepository.save(purchase);

    return { message: "Purchase deleted successfully" };
  }

  /**
   * Add products to an existing purchase
   */
  async addProducts(uuid: string, items: any[]): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['purchaseItems'],
    });

    if (!purchase) {
      throw new NotFoundException(`Purchase with UUID ${uuid} not found`);
    }

    const validatedItems = await this.validateAndTransformItems(items);

    // Create new purchase items
    const purchaseItems = validatedItems.map((item, index) => {
      const purchaseItem = new PurchaseItem();
      purchaseItem.id = PurchaseItem.generateId();
      purchaseItem.purchaseUuid = uuid;
      purchaseItem.productUuid = item.productUuid;
      purchaseItem.name = item.name;
      purchaseItem.quantity = item.quantity;
      purchaseItem.unitPrice = item.unitPrice;
      purchaseItem.lineTotal = item.lineTotal;
      purchaseItem.taxAmount = purchase.useTax ? this.calculateTaxAmount(item.lineTotal, purchase.taxRate) : 0;
      purchaseItem.order = (purchase.purchaseItems?.length || 0) + index;
      return purchaseItem;
    });

    await this.purchaseItemRepository.save(purchaseItems);

    // Recalculate totals
    const allItems = [...(purchase.purchaseItems || []), ...purchaseItems];
    const subtotal = this.roundToTwoDecimals(
      allItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );
    const taxAmount = purchase.useTax ? this.calculateTaxAmount(subtotal, purchase.taxRate) : 0;
    const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);

    purchase.subtotal = subtotal;
    purchase.taxAmount = taxAmount;
    purchase.totalAmount = totalAmount;
    purchase.balanceDue = this.roundToTwoDecimals(totalAmount - purchase.amountPaid);

    await this.purchaseRepository.save(purchase);

    return await this.findOne(uuid);
  }

  /**
   * Validate and transform items for purchase
   */
  private async validateAndTransformItems(items: any[]): Promise<any[]> {
    const validatedItems = [];

    for (const item of items) {
      if (!item.productUuid || !item.name || !item.quantity || !item.unitPrice) {
        throw new BadRequestException(
          "Each item must have productUuid, name, quantity, and unitPrice"
        );
      }

      // Check if product exists
      const product = await this.productRepository.findOne({
        where: { id: item.productUuid, isDeleted: false }
      });

      if (!product) {
        throw new NotFoundException(`Product with UUID ${item.productUuid} not found`);
      }

      const lineTotal = this.roundToTwoDecimals(item.quantity * item.unitPrice);

      validatedItems.push({
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal,
        order: item.order || 0,
      });
    }

    return validatedItems;
  }

  /**
   * Set payment for a purchase
   */
  async setPayment(uuid: string, paymentMethod: string, amountPaid: number): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!purchase) {
      throw new NotFoundException(`Purchase with UUID ${uuid} not found`);
    }

    purchase.paymentMethod = paymentMethod as PaymentMethods;
    purchase.amountPaid = amountPaid;
    purchase.balanceDue = this.roundToTwoDecimals(purchase.totalAmount - amountPaid);
    purchase.status = this.determinePurchaseStatus(amountPaid, purchase.balanceDue);

    // Add payment date
    if (!purchase.paymentDate) {
      purchase.paymentDate = [];
    }
    purchase.paymentDate.push(new Date());

    await this.purchaseRepository.save(purchase);

    return await this.findOne(uuid);
  }

  /**
   * Cancel a purchase
   */
  async cancelPurchase(uuid: string, userUuid: string): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!purchase) {
      throw new NotFoundException(`Purchase with UUID ${uuid} not found`);
    }

    purchase.status = PurchaseStatus.CANCELLED;
    purchase.updatedBy = userUuid;

    await this.purchaseRepository.save(purchase);

    return await this.findOne(uuid);
  }

  /**
   * Determine purchase status based on payment
   */
  /**
   * Validate if a status transition is allowed
   */
  private validateStatusTransition(currentStatus: PurchaseStatus, newStatus: PurchaseStatus): boolean {
    const allowedTransitions = PURCHASE_STATUS_FLOW[currentStatus] || [];
    return allowedTransitions.includes(newStatus);
  }

  /**
   * Get allowed status transitions for current status
   */
  getAllowedStatusTransitions(currentStatus: PurchaseStatus): PurchaseStatus[] {
    return PURCHASE_STATUS_FLOW[currentStatus] || [];
  }

  private determinePurchaseStatus(amountPaid: number, balanceDue: number): PurchaseStatus {
    if (balanceDue <= 0) {
      return PurchaseStatus.PAID;
    } else if (amountPaid > 0) {
      return PurchaseStatus.PARTIALLY_PAID;
    } else {
      return PurchaseStatus.DRAFT;
    }
  }

  /**
   * Get purchase analytics for a supplier
   */
  async getSupplierAnalytics(supplierUuid: string, dateRange?: { startDate: Date; endDate: Date }) {
    const query = this.purchaseRepository
      .createQueryBuilder('purchase')
      .where('purchase.supplierUuid = :supplierUuid', { supplierUuid })
      .andWhere('purchase.isDeleted = false');

    if (dateRange) {
      query.andWhere('purchase.createdAt BETWEEN :startDate AND :endDate', dateRange);
    }

    const purchases = await query.getMany();

    const analytics = {
      totalPurchases: purchases.length,
      totalAmount: 0,
      averageOrderValue: 0,
      statusBreakdown: {},
      monthlyTrend: {},
      paymentMethodBreakdown: {},
      topProducts: {},
    };

    purchases.forEach(purchase => {
      analytics.totalAmount += purchase.totalAmount;
      
      // Status breakdown
      analytics.statusBreakdown[purchase.status] = (analytics.statusBreakdown[purchase.status] || 0) + 1;
      
      // Payment method breakdown
      analytics.paymentMethodBreakdown[purchase.paymentMethod] = (analytics.paymentMethodBreakdown[purchase.paymentMethod] || 0) + 1;
      
      // Monthly trend
      const month = purchase.createdAt.toISOString().substring(0, 7);
      analytics.monthlyTrend[month] = (analytics.monthlyTrend[month] || 0) + purchase.totalAmount;
    });

    analytics.averageOrderValue = analytics.totalPurchases > 0 ? analytics.totalAmount / analytics.totalPurchases : 0;

    // Get top products
    const purchaseItems = await this.purchaseItemRepository
      .createQueryBuilder('item')
      .leftJoin('item.purchase', 'purchase')
      .where('purchase.supplierUuid = :supplierUuid', { supplierUuid })
      .andWhere('purchase.isDeleted = false')
      .select(['item.name', 'SUM(item.quantity) as totalQuantity', 'SUM(item.lineTotal) as totalValue'])
      .groupBy('item.name')
      .orderBy('totalValue', 'DESC')
      .limit(10)
      .getRawMany();

    analytics.topProducts = purchaseItems;

    return analytics;
  }

  /**
   * Get purchase analytics for a warehouse
   */
  async getWarehouseAnalytics(warehouseUuid: string, dateRange?: { startDate: Date; endDate: Date }) {
    const query = this.purchaseRepository
      .createQueryBuilder('purchase')
      .where('purchase.warehouseUuid = :warehouseUuid', { warehouseUuid })
      .andWhere('purchase.isDeleted = false');

    if (dateRange) {
      query.andWhere('purchase.createdAt BETWEEN :startDate AND :endDate', dateRange);
    }

    const purchases = await query.getMany();

    const analytics = {
      totalPurchases: purchases.length,
      totalAmount: 0,
      averageOrderValue: 0,
      statusBreakdown: {},
      supplierBreakdown: {},
      monthlyTrend: {},
      pendingApprovals: 0,
      pendingDeliveries: 0,
    };

    purchases.forEach(purchase => {
      analytics.totalAmount += purchase.totalAmount;
      
      // Status breakdown
      analytics.statusBreakdown[purchase.status] = (analytics.statusBreakdown[purchase.status] || 0) + 1;
      
      // Supplier breakdown
      analytics.supplierBreakdown[purchase.supplierName] = (analytics.supplierBreakdown[purchase.supplierName] || 0) + 1;
      
      // Monthly trend
      const month = purchase.createdAt.toISOString().substring(0, 7);
      analytics.monthlyTrend[month] = (analytics.monthlyTrend[month] || 0) + purchase.totalAmount;
      
      // Count pending items
      if (purchase.status === PurchaseStatus.PENDING_APPROVAL) {
        analytics.pendingApprovals++;
      }
      if (purchase.status === PurchaseStatus.ORDERED || purchase.status === PurchaseStatus.PARTIALLY_RECEIVED) {
        analytics.pendingDeliveries++;
      }
    });

    analytics.averageOrderValue = analytics.totalPurchases > 0 ? analytics.totalAmount / analytics.totalPurchases : 0;

    return analytics;
  }

  /**
   * Approve a purchase order
   */
  async approvePurchase(uuid: string, userUuid: string, approvalNotes?: string): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!purchase) {
      throw new NotFoundException('Purchase not found');
    }

    if (purchase.status !== PurchaseStatus.PENDING_APPROVAL) {
      throw new BadRequestException('Purchase must be in PENDING_APPROVAL status to be approved');
    }

    // Update purchase status
    purchase.status = PurchaseStatus.APPROVED;
    purchase.updatedBy = userUuid;
    
    if (approvalNotes) {
      purchase.notes = purchase.notes ? `${purchase.notes}\n[APPROVAL] ${approvalNotes}` : `[APPROVAL] ${approvalNotes}`;
    }

    const updatedPurchase = await this.purchaseRepository.save(purchase);
    return toPurchaseResponseDto(updatedPurchase);
  }

  /**
   * Reject a purchase order
   */
  async rejectPurchase(uuid: string, userUuid: string, rejectionReason: string): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!purchase) {
      throw new NotFoundException('Purchase not found');
    }

    if (purchase.status !== PurchaseStatus.PENDING_APPROVAL) {
      throw new BadRequestException('Purchase must be in PENDING_APPROVAL status to be rejected');
    }

    // Update purchase status
    purchase.status = PurchaseStatus.CANCELLED;
    purchase.updatedBy = userUuid;
    purchase.notes = purchase.notes ? `${purchase.notes}\n[REJECTED] ${rejectionReason}` : `[REJECTED] ${rejectionReason}`;

    const updatedPurchase = await this.purchaseRepository.save(purchase);
    return toPurchaseResponseDto(updatedPurchase);
  }

  /**
   * Mark purchase as ordered (sent to supplier)
   */
  async markAsOrdered(uuid: string, userUuid: string, orderDate?: Date): Promise<PurchaseResponseDto> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!purchase) {
      throw new NotFoundException('Purchase not found');
    }

    if (purchase.status !== PurchaseStatus.APPROVED) {
      throw new BadRequestException('Purchase must be in APPROVED status to be marked as ordered');
    }

    // Update purchase status
    purchase.status = PurchaseStatus.ORDERED;
    purchase.updatedBy = userUuid;
    purchase.purchaseDate = orderDate || new Date();

    const updatedPurchase = await this.purchaseRepository.save(purchase);
    return toPurchaseResponseDto(updatedPurchase);
  }

  /**
   * Get purchases pending approval
   */
  async getPendingApprovals(warehouseUuid?: string): Promise<PurchaseResponseDto[]> {
    const query = this.purchaseRepository
      .createQueryBuilder('purchase')
      .where('purchase.status = :status', { status: PurchaseStatus.PENDING_APPROVAL })
      .andWhere('purchase.isDeleted = false');

    if (warehouseUuid) {
      query.andWhere('purchase.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    query.orderBy('purchase.createdAt', 'ASC');

    const purchases = await query.getMany();
    return toPurchaseResponseDtoArray(purchases);
  }

  /**
   * Get purchases pending delivery
   */
  async getPendingDeliveries(warehouseUuid?: string): Promise<PurchaseResponseDto[]> {
    const query = this.purchaseRepository
      .createQueryBuilder('purchase')
      .where('purchase.status IN (:...statuses)', { 
        statuses: [PurchaseStatus.ORDERED, PurchaseStatus.PARTIALLY_RECEIVED] 
      })
      .andWhere('purchase.isDeleted = false');

    if (warehouseUuid) {
      query.andWhere('purchase.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    query.orderBy('purchase.expectedDeliveryDate', 'ASC');

    const purchases = await query.getMany();
    return toPurchaseResponseDtoArray(purchases);
  }

  /**
   * Get overdue purchases
   */
  async getOverduePurchases(warehouseUuid?: string): Promise<PurchaseResponseDto[]> {
    const query = this.purchaseRepository
      .createQueryBuilder('purchase')
      .where('purchase.expectedDeliveryDate < :today', { today: new Date() })
      .andWhere('purchase.status IN (:...statuses)', { 
        statuses: [PurchaseStatus.ORDERED, PurchaseStatus.PARTIALLY_RECEIVED] 
      })
      .andWhere('purchase.isDeleted = false');

    if (warehouseUuid) {
      query.andWhere('purchase.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    query.orderBy('purchase.expectedDeliveryDate', 'ASC');

    const purchases = await query.getMany();
    return toPurchaseResponseDtoArray(purchases);
  }

  /**
   * Get purchase summary statistics
   */
  async getPurchaseSummary(dateRange?: { startDate: Date; endDate: Date }) {
    const query = this.purchaseRepository
      .createQueryBuilder('purchase')
      .where('purchase.isDeleted = false');

    if (dateRange) {
      query.andWhere('purchase.createdAt BETWEEN :startDate AND :endDate', dateRange);
    }

    const purchases = await query.getMany();

    const summary = {
      totalPurchases: purchases.length,
      totalAmount: 0,
      averageOrderValue: 0,
      statusCounts: {},
      supplierCount: new Set<string>(),
      warehouseCount: new Set<string>(),
      pendingApprovals: 0,
      pendingDeliveries: 0,
      overdueDeliveries: 0,
    };

    purchases.forEach(purchase => {
      summary.totalAmount += purchase.totalAmount;
      summary.statusCounts[purchase.status] = (summary.statusCounts[purchase.status] || 0) + 1;
      
      if (purchase.supplierUuid) summary.supplierCount.add(purchase.supplierUuid);
      if (purchase.warehouseUuid) summary.warehouseCount.add(purchase.warehouseUuid);
      
      if (purchase.status === PurchaseStatus.PENDING_APPROVAL) summary.pendingApprovals++;
      if (purchase.status === PurchaseStatus.ORDERED || purchase.status === PurchaseStatus.PARTIALLY_RECEIVED) {
        summary.pendingDeliveries++;
        if (purchase.expectedDeliveryDate < new Date()) {
          summary.overdueDeliveries++;
        }
      }
    });

    summary.averageOrderValue = summary.totalPurchases > 0 ? summary.totalAmount / summary.totalPurchases : 0;
    const supplierCount = summary.supplierCount.size;
    const warehouseCount = summary.warehouseCount.size;
    
    return {
      ...summary,
      supplierCount,
      warehouseCount,
    };

    return summary;
  }
} 