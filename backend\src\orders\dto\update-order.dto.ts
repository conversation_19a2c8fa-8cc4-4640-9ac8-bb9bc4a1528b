import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsUUID,
  IsOptional,
  IsDateString,
  IsEnum,
  IsBoolean,
  IsN<PERSON>ber,
  Min,
  <PERSON>,
} from "class-validator";
import { OrderStatus, OrderPriority } from "../order.entity";

export class UpdateOrderDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the customer",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID("7")
  customerUuid?: string;

  @ApiProperty({
    example: "2024-01-20T10:00:00.000Z",
    description: "Requested delivery date",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  requestedDeliveryDate?: string;

  @ApiProperty({
    example: "2024-01-22T14:30:00.000Z",
    description: "Actual delivery date",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  actualDeliveryDate?: string;

  @ApiProperty({
    example: OrderStatus.PROCESSING,
    description: "Order status",
    enum: OrderStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @ApiProperty({
    example: OrderPriority.HIGH,
    description: "Order priority level",
    enum: OrderPriority,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderPriority)
  priority?: OrderPriority;

  @ApiProperty({
    example: "Updated delivery instructions",
    description: "Order notes",
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    example: true,
    description: "Whether tax is applied to this order",
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  useTax?: boolean;

  @ApiProperty({
    example: 0.1,
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  taxRate?: number;
}
