[{"C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\authApi.ts": "1", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\callback\\page.tsx": "2", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\DashboardCard.tsx": "4", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\QuickActionButton.tsx": "5", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\RecentActivity.tsx": "6", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\dashboardApi.ts": "7", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\hooks\\useDashboardData.ts": "8", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\layout.tsx": "9", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\page.tsx": "10", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\layout.tsx": "11", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\categories\\page.tsx": "12", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\create.tsx": "13", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productCategoriesApi.ts": "15", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductForm.tsx": "16", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductModal.tsx": "17", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productsApi.ts": "18", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\[uuid]\\edit.tsx": "19", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\api.ts": "20", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\components\\ProductSelectionModal.tsx": "21", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\page.tsx": "22", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\alerts\\page.tsx": "23", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\levels\\page.tsx": "24", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\movements\\page.tsx": "25", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\transfers\\page.tsx": "26", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\vans\\page.tsx": "27", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\api.ts": "28", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\hooks\\useStockLevelsData.ts": "29", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\page.tsx": "30", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\layout.tsx": "31", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\layout.tsx": "32", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\api.ts": "33", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\page.tsx": "34", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\RegionForm.tsx": "35", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\api.ts": "36", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\ComputeRouteModal.tsx": "37", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteList.tsx": "38", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteMap.tsx": "39", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\page.tsx": "40", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-loading\\page.tsx": "41", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-stock\\page.tsx": "42", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\page.tsx": "43", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanForm.tsx": "44", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanModal.tsx": "45", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\vansApi.ts": "46", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\warehouses\\page.tsx": "47", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\page.tsx": "48", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\providers.tsx": "49", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\page.tsx": "50", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\goods-receipt\\page.tsx": "51", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\layout.tsx": "52", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\orders\\page.tsx": "53", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\returns\\page.tsx": "54", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierModal.tsx": "55", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierSearchBar.tsx": "56", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SuppliersTable.tsx": "57", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\page.tsx": "58", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\suppliersApi.ts": "59", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\types.ts": "60", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\financial\\page.tsx": "61", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\inventory\\page.tsx": "62", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\layout.tsx": "63", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\sales\\page.tsx": "64", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\van-performance\\page.tsx": "65", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\index.ts": "66", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\InvoicePrint.tsx": "67", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\CustomerTableActions.tsx": "68", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\index.ts": "69", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\SearchAndFilters.tsx": "70", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerDetailsModal.tsx": "71", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerModal.tsx": "72", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customerPaymentsApi.ts": "73", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customersApi.ts": "74", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\index.ts": "75", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerActions.ts": "76", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerFilters.ts": "77", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\CustomerMap.tsx": "78", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\page.tsx": "79", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\page.tsx": "80", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentDetailsModal.tsx": "81", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentModal.tsx": "82", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\page.tsx": "83", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomerPayments.ts": "84", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomers.ts": "85", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\layout.tsx": "86", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\index.ts": "87", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\ListComponent.tsx": "88", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSComponent.tsx": "89", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSView.tsx": "90", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SaleDetailsModal.tsx": "91", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesCancelModal.tsx": "92", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesFilters.tsx": "93", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesHeader.tsx": "94", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesListView.tsx": "95", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\index.ts": "96", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesActions.ts": "97", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesData.ts": "98", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesPOSState.ts": "99", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\page.tsx": "100", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\CategoryFilter.tsx": "101", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\index.ts": "102", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\NotesControls.tsx": "103", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\Pagination.tsx": "104", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\PaymentSelector.tsx": "105", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\ProductList.tsx": "106", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\QuantityModal.tsx": "107", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SalesCart.tsx": "108", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SearchBar.tsx": "109", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\TaxControls.tsx": "110", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\config\\posConfig.ts": "111", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\index.ts": "112", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\useKeyboardNavigation.ts": "113", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSOperations.ts": "114", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSProducts.ts": "115", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSState.ts": "116", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\posApi.ts": "117", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\styles\\posStyles.ts": "118", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\types\\index.ts": "119", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\utils\\posHelpers.ts": "120", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesApi.ts": "121", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesHelpers.ts": "122", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\accountSettingsApi.ts": "123", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\companiesApi.ts": "124", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\data\\page.tsx": "125", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\layout.tsx": "126", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\profile\\page.tsx": "127", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\page.tsx": "128", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\rolesApi.ts": "129", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\system\\page.tsx": "130", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\AddUserModal.tsx": "131", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\DeleteUserDialog.tsx": "132", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\EditUserModal.tsx": "133", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserDetailsModal.tsx": "134", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserTable.tsx": "135", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\page.tsx": "136", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\usersApi.ts": "137", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\warehousesApi.ts": "138", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\BackendStatusChecker.tsx": "139", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\api\\customerApi.ts": "140", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\CustomerModal.tsx": "141", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\hooks\\useCustomerData.ts": "142", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\index.ts": "143", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\styles\\customerModalStyles.ts": "144", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\types\\index.ts": "145", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\utils\\customerHelpers.ts": "146", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTable.tsx": "147", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableLogic.ts": "148", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableStyles.tsx": "149", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ErrorToast.tsx": "150", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\ItemsTable.tsx": "151", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\LoadingDemo.tsx": "152", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\TableActionButtons.tsx": "153", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\NetworkStatusChecker.tsx": "154", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ProtectedRoute.tsx": "155", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SideTaskBar\\SideTaskBar.tsx": "156", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\TopTaskBar\\TopTaskBar.tsx": "157", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\LogDetailsModal.tsx": "158", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\layout.tsx": "159", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\logsApi.ts": "160", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\page.tsx": "161", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\index.ts": "162", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\UserModal.tsx": "163", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\test-diff\\page.tsx": "164", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\entityDisplayConfig.ts": "165", "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\examples\\newEntityExample.ts": "166"}, {"size": 5455, "mtime": 1753726786583, "results": "167", "hashOfConfig": "168"}, {"size": 17268, "mtime": 1753559813872, "results": "169", "hashOfConfig": "168"}, {"size": 16195, "mtime": 1753559619509, "results": "170", "hashOfConfig": "168"}, {"size": 2255, "mtime": 1753197132803, "results": "171", "hashOfConfig": "168"}, {"size": 1442, "mtime": 1753197132803, "results": "172", "hashOfConfig": "168"}, {"size": 5289, "mtime": 1753197132804, "results": "173", "hashOfConfig": "168"}, {"size": 4403, "mtime": 1753726771130, "results": "174", "hashOfConfig": "168"}, {"size": 6103, "mtime": 1753724209627, "results": "175", "hashOfConfig": "168"}, {"size": 515, "mtime": 1753197132805, "results": "176", "hashOfConfig": "168"}, {"size": 8445, "mtime": 1753724102931, "results": "177", "hashOfConfig": "168"}, {"size": 627, "mtime": 1753197132805, "results": "178", "hashOfConfig": "168"}, {"size": 12628, "mtime": 1753436167332, "results": "179", "hashOfConfig": "168"}, {"size": 3128, "mtime": 1753437290519, "results": "180", "hashOfConfig": "168"}, {"size": 21177, "mtime": 1753437289410, "results": "181", "hashOfConfig": "168"}, {"size": 4912, "mtime": 1753197132807, "results": "182", "hashOfConfig": "168"}, {"size": 29276, "mtime": 1753437289458, "results": "183", "hashOfConfig": "168"}, {"size": 1765, "mtime": 1753197132806, "results": "184", "hashOfConfig": "168"}, {"size": 11257, "mtime": 1753437290514, "results": "185", "hashOfConfig": "168"}, {"size": 4763, "mtime": 1753437291482, "results": "186", "hashOfConfig": "168"}, {"size": 4048, "mtime": 1753436167336, "results": "187", "hashOfConfig": "168"}, {"size": 14144, "mtime": 1753436167337, "results": "188", "hashOfConfig": "168"}, {"size": 17067, "mtime": 1753436167337, "results": "189", "hashOfConfig": "168"}, {"size": 265, "mtime": 1753197132809, "results": "190", "hashOfConfig": "168"}, {"size": 254, "mtime": 1753197132809, "results": "191", "hashOfConfig": "168"}, {"size": 261, "mtime": 1753197132810, "results": "192", "hashOfConfig": "168"}, {"size": 263, "mtime": 1753197132810, "results": "193", "hashOfConfig": "168"}, {"size": 246, "mtime": 1753197132810, "results": "194", "hashOfConfig": "168"}, {"size": 7448, "mtime": 1753436167333, "results": "195", "hashOfConfig": "168"}, {"size": 9735, "mtime": 1753436167334, "results": "196", "hashOfConfig": "168"}, {"size": 10114, "mtime": 1753436167335, "results": "197", "hashOfConfig": "168"}, {"size": 711, "mtime": 1753205970784, "results": "198", "hashOfConfig": "168"}, {"size": 627, "mtime": 1753197132811, "results": "199", "hashOfConfig": "168"}, {"size": 2396, "mtime": 1753197132811, "results": "200", "hashOfConfig": "168"}, {"size": 13981, "mtime": 1753197132812, "results": "201", "hashOfConfig": "168"}, {"size": 9041, "mtime": 1753197132811, "results": "202", "hashOfConfig": "168"}, {"size": 3636, "mtime": 1753197132812, "results": "203", "hashOfConfig": "168"}, {"size": 9653, "mtime": 1753197132812, "results": "204", "hashOfConfig": "168"}, {"size": 5407, "mtime": 1753197132813, "results": "205", "hashOfConfig": "168"}, {"size": 17173, "mtime": 1753197132813, "results": "206", "hashOfConfig": "168"}, {"size": 12871, "mtime": 1753197132813, "results": "207", "hashOfConfig": "168"}, {"size": 213, "mtime": 1753197132814, "results": "208", "hashOfConfig": "168"}, {"size": 205, "mtime": 1753197132814, "results": "209", "hashOfConfig": "168"}, {"size": 10247, "mtime": 1753197132815, "results": "210", "hashOfConfig": "168"}, {"size": 7985, "mtime": 1753197132815, "results": "211", "hashOfConfig": "168"}, {"size": 3666, "mtime": 1753197132815, "results": "212", "hashOfConfig": "168"}, {"size": 1953, "mtime": 1753197132815, "results": "213", "hashOfConfig": "168"}, {"size": 12119, "mtime": 1753205023060, "results": "214", "hashOfConfig": "168"}, {"size": 911, "mtime": 1753197132816, "results": "215", "hashOfConfig": "168"}, {"size": 2318, "mtime": 1753205023085, "results": "216", "hashOfConfig": "168"}, {"size": 3459, "mtime": 1753763829303, "results": "217", "hashOfConfig": "168"}, {"size": 258, "mtime": 1753197132822, "results": "218", "hashOfConfig": "168"}, {"size": 628, "mtime": 1753197132822, "results": "219", "hashOfConfig": "168"}, {"size": 504, "mtime": 1753197132822, "results": "220", "hashOfConfig": "168"}, {"size": 267, "mtime": 1753197132823, "results": "221", "hashOfConfig": "168"}, {"size": 8134, "mtime": 1753197132823, "results": "222", "hashOfConfig": "168"}, {"size": 1602, "mtime": 1753197132823, "results": "223", "hashOfConfig": "168"}, {"size": 1876, "mtime": 1753197132824, "results": "224", "hashOfConfig": "168"}, {"size": 8905, "mtime": 1753197132824, "results": "225", "hashOfConfig": "168"}, {"size": 1695, "mtime": 1753197132824, "results": "226", "hashOfConfig": "168"}, {"size": 945, "mtime": 1753197132824, "results": "227", "hashOfConfig": "168"}, {"size": 237, "mtime": 1753197132825, "results": "228", "hashOfConfig": "168"}, {"size": 237, "mtime": 1753197132825, "results": "229", "hashOfConfig": "168"}, {"size": 625, "mtime": 1753197132825, "results": "230", "hashOfConfig": "168"}, {"size": 231, "mtime": 1753197132825, "results": "231", "hashOfConfig": "168"}, {"size": 229, "mtime": 1753197132826, "results": "232", "hashOfConfig": "168"}, {"size": 58, "mtime": 1753775800357, "results": "233", "hashOfConfig": "168"}, {"size": 14454, "mtime": 1753436167338, "results": "234", "hashOfConfig": "168"}, {"size": 2234, "mtime": 1753197132828, "results": "235", "hashOfConfig": "168"}, {"size": 119, "mtime": 1753197132829, "results": "236", "hashOfConfig": "168"}, {"size": 9699, "mtime": 1753197132829, "results": "237", "hashOfConfig": "168"}, {"size": 13821, "mtime": 1753436167339, "results": "238", "hashOfConfig": "168"}, {"size": 17006, "mtime": 1753436167339, "results": "239", "hashOfConfig": "168"}, {"size": 13816, "mtime": 1753775720323, "results": "240", "hashOfConfig": "168"}, {"size": 12722, "mtime": 1753436167341, "results": "241", "hashOfConfig": "168"}, {"size": 119, "mtime": 1753197132830, "results": "242", "hashOfConfig": "168"}, {"size": 4522, "mtime": 1753197132830, "results": "243", "hashOfConfig": "168"}, {"size": 4625, "mtime": 1753197132831, "results": "244", "hashOfConfig": "168"}, {"size": 11629, "mtime": 1753776928250, "results": "245", "hashOfConfig": "168"}, {"size": 13038, "mtime": 1753436167342, "results": "246", "hashOfConfig": "168"}, {"size": 7315, "mtime": 1753436167343, "results": "247", "hashOfConfig": "168"}, {"size": 16383, "mtime": 1753436167343, "results": "248", "hashOfConfig": "168"}, {"size": 17660, "mtime": 1753197132833, "results": "249", "hashOfConfig": "168"}, {"size": 25184, "mtime": 1753436167344, "results": "250", "hashOfConfig": "168"}, {"size": 10457, "mtime": 1753197132834, "results": "251", "hashOfConfig": "168"}, {"size": 4980, "mtime": 1753197132834, "results": "252", "hashOfConfig": "168"}, {"size": 623, "mtime": 1753197132835, "results": "253", "hashOfConfig": "168"}, {"size": 549, "mtime": 1753776874146, "results": "254", "hashOfConfig": "168"}, {"size": 2466, "mtime": 1753197406252, "results": "255", "hashOfConfig": "168"}, {"size": 803, "mtime": 1753197406253, "results": "256", "hashOfConfig": "168"}, {"size": 22992, "mtime": 1753615162570, "results": "257", "hashOfConfig": "168"}, {"size": 5923, "mtime": 1753775638391, "results": "258", "hashOfConfig": "168"}, {"size": 1655, "mtime": 1753197132838, "results": "259", "hashOfConfig": "168"}, {"size": 12948, "mtime": 1753197132839, "results": "260", "hashOfConfig": "168"}, {"size": 3487, "mtime": 1753197132839, "results": "261", "hashOfConfig": "168"}, {"size": 7391, "mtime": 1753776873104, "results": "262", "hashOfConfig": "168"}, {"size": 182, "mtime": 1753197406254, "results": "263", "hashOfConfig": "168"}, {"size": 8520, "mtime": 1753777414581, "results": "264", "hashOfConfig": "168"}, {"size": 2884, "mtime": 1753436167346, "results": "265", "hashOfConfig": "168"}, {"size": 4058, "mtime": 1753436167347, "results": "266", "hashOfConfig": "168"}, {"size": 7434, "mtime": 1753436167348, "results": "267", "hashOfConfig": "168"}, {"size": 3956, "mtime": 1753197406256, "results": "268", "hashOfConfig": "168"}, {"size": 423, "mtime": 1753197132844, "results": "269", "hashOfConfig": "168"}, {"size": 1733, "mtime": 1753197132842, "results": "270", "hashOfConfig": "168"}, {"size": 5085, "mtime": 1753197406256, "results": "271", "hashOfConfig": "168"}, {"size": 4743, "mtime": 1753776873147, "results": "272", "hashOfConfig": "168"}, {"size": 11122, "mtime": 1753776892007, "results": "273", "hashOfConfig": "168"}, {"size": 5677, "mtime": 1753197132843, "results": "274", "hashOfConfig": "168"}, {"size": 16880, "mtime": 1753776873141, "results": "275", "hashOfConfig": "168"}, {"size": 2813, "mtime": 1753436167350, "results": "276", "hashOfConfig": "168"}, {"size": 2710, "mtime": 1753197406258, "results": "277", "hashOfConfig": "168"}, {"size": 2938, "mtime": 1753197132845, "results": "278", "hashOfConfig": "168"}, {"size": 552, "mtime": 1753197132846, "results": "279", "hashOfConfig": "168"}, {"size": 2266, "mtime": 1753197132846, "results": "280", "hashOfConfig": "168"}, {"size": 8418, "mtime": 1753714639903, "results": "281", "hashOfConfig": "168"}, {"size": 11960, "mtime": 1753197406259, "results": "282", "hashOfConfig": "168"}, {"size": 20473, "mtime": 1753714766175, "results": "283", "hashOfConfig": "168"}, {"size": 19159, "mtime": 1753768203409, "results": "284", "hashOfConfig": "168"}, {"size": 7958, "mtime": 1753197132847, "results": "285", "hashOfConfig": "168"}, {"size": 6849, "mtime": 1753714614257, "results": "286", "hashOfConfig": "168"}, {"size": 7124, "mtime": 1753331631171, "results": "287", "hashOfConfig": "168"}, {"size": 5780, "mtime": 1753776872085, "results": "288", "hashOfConfig": "168"}, {"size": 1374, "mtime": 1753776874115, "results": "289", "hashOfConfig": "168"}, {"size": 3432, "mtime": 1753197132849, "results": "290", "hashOfConfig": "168"}, {"size": 2225, "mtime": 1753777360382, "results": "291", "hashOfConfig": "168"}, {"size": 229, "mtime": 1753197132850, "results": "292", "hashOfConfig": "168"}, {"size": 626, "mtime": 1753197132850, "results": "293", "hashOfConfig": "168"}, {"size": 34646, "mtime": 1753197132851, "results": "294", "hashOfConfig": "168"}, {"size": 9490, "mtime": 1753197132851, "results": "295", "hashOfConfig": "168"}, {"size": 3004, "mtime": 1753197132851, "results": "296", "hashOfConfig": "168"}, {"size": 229, "mtime": 1753197132852, "results": "297", "hashOfConfig": "168"}, {"size": 6132, "mtime": 1753197132852, "results": "298", "hashOfConfig": "168"}, {"size": 1430, "mtime": 1753197132852, "results": "299", "hashOfConfig": "168"}, {"size": 4365, "mtime": 1753197132853, "results": "300", "hashOfConfig": "168"}, {"size": 6114, "mtime": 1753197132853, "results": "301", "hashOfConfig": "168"}, {"size": 7407, "mtime": 1753197132853, "results": "302", "hashOfConfig": "168"}, {"size": 9567, "mtime": 1753197132853, "results": "303", "hashOfConfig": "168"}, {"size": 8420, "mtime": 1753197132854, "results": "304", "hashOfConfig": "168"}, {"size": 5033, "mtime": 1753205022871, "results": "305", "hashOfConfig": "168"}, {"size": 5847, "mtime": 1753197132854, "results": "306", "hashOfConfig": "168"}, {"size": 2615, "mtime": 1753436167355, "results": "307", "hashOfConfig": "168"}, {"size": 17541, "mtime": 1753436167354, "results": "308", "hashOfConfig": "168"}, {"size": 5831, "mtime": 1753197132855, "results": "309", "hashOfConfig": "168"}, {"size": 642, "mtime": 1753197132856, "results": "310", "hashOfConfig": "168"}, {"size": 2513, "mtime": 1753197132856, "results": "311", "hashOfConfig": "168"}, {"size": 1810, "mtime": 1753197406261, "results": "312", "hashOfConfig": "168"}, {"size": 2468, "mtime": 1753197132856, "results": "313", "hashOfConfig": "168"}, {"size": 10624, "mtime": 1753197132859, "results": "314", "hashOfConfig": "168"}, {"size": 3420, "mtime": 1753197132859, "results": "315", "hashOfConfig": "168"}, {"size": 5127, "mtime": 1753197132860, "results": "316", "hashOfConfig": "168"}, {"size": 454, "mtime": 1753197132857, "results": "317", "hashOfConfig": "168"}, {"size": 14634, "mtime": 1753467887894, "results": "318", "hashOfConfig": "168"}, {"size": 11861, "mtime": 1753197132860, "results": "319", "hashOfConfig": "168"}, {"size": 2680, "mtime": 1753197132861, "results": "320", "hashOfConfig": "168"}, {"size": 9245, "mtime": 1753197132857, "results": "321", "hashOfConfig": "168"}, {"size": 737, "mtime": 1753201780688, "results": "322", "hashOfConfig": "168"}, {"size": 14707, "mtime": 1753775728450, "results": "323", "hashOfConfig": "168"}, {"size": 10177, "mtime": 1753436167356, "results": "324", "hashOfConfig": "168"}, {"size": 20657, "mtime": 1753723913759, "results": "325", "hashOfConfig": "168"}, {"size": 519, "mtime": 1753467890059, "results": "326", "hashOfConfig": "168"}, {"size": 5510, "mtime": 1753651530696, "results": "327", "hashOfConfig": "168"}, {"size": 17153, "mtime": 1753723938717, "results": "328", "hashOfConfig": "168"}, {"size": 93, "mtime": 1753467890146, "results": "329", "hashOfConfig": "168"}, {"size": 11130, "mtime": 1753467890115, "results": "330", "hashOfConfig": "168"}, {"size": 6495, "mtime": 1753651321823, "results": "331", "hashOfConfig": "168"}, {"size": 15518, "mtime": 1753723983031, "results": "332", "hashOfConfig": "168"}, {"size": 5736, "mtime": 1753724050562, "results": "333", "hashOfConfig": "168"}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1f0kge6", {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\authApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\callback\\page.tsx", ["832"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\auth\\page.tsx", [], ["833"], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\DashboardCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\QuickActionButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\components\\RecentActivity.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\dashboardApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\hooks\\useDashboardData.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\dashboard\\page.tsx", ["834"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\categories\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\create.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\page.tsx", ["835"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productCategoriesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductForm.tsx", ["836", "837"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\ProductModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\productsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\products\\[uuid]\\edit.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\components\\ProductSelectionModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\adjustments\\page.tsx", [], ["838", "839"], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\alerts\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\levels\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\movements\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\transfers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock\\vans\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\hooks\\useStockLevelsData.ts", ["840", "841"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\inventory\\stock-levels\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\regions\\RegionForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\api.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\ComputeRouteModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\components\\RouteMap.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\routes\\page.tsx", ["842"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-loading\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\van-stock\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanForm.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\VanModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\vans\\vansApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logistics\\warehouses\\page.tsx", ["843", "844", "845"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchase\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\goods-receipt\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\returns\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SupplierSearchBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\components\\SuppliersTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\suppliersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\purchasing\\suppliers\\types.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\financial\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\inventory\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\reports\\van-performance\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\components\\InvoicePrint.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\CustomerTableActions.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\components\\SearchAndFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\CustomerModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customerPaymentsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\customersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerActions.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\hooks\\useCustomerFilters.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\CustomerMap.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\locations\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\CustomerPaymentModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\payments\\page.tsx", ["846", "847", "848", "849"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomerPayments.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\customers\\useCustomers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\ListComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSComponent.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\POSView.tsx", ["850", "851", "852", "853", "854", "855", "856", "857", "858", "859", "860", "861", "862"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SaleDetailsModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesCancelModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesFilters.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesHeader.tsx", ["863"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\components\\SalesListView.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesActions.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesData.ts", ["864", "865", "866", "867"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\hooks\\useSalesPOSState.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\CategoryFilter.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\NotesControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\Pagination.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\PaymentSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\ProductList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\QuantityModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SalesCart.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\SearchBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\components\\TaxControls.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\config\\posConfig.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\useKeyboardNavigation.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSOperations.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSProducts.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\hooks\\usePOSState.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\posApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\styles\\posStyles.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\pos\\utils\\posHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\sales\\sales\\salesHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\accountSettingsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\companiesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\data\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\roles\\rolesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\system\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\AddUserModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\DeleteUserDialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\EditUserModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserDetailsModal.tsx", ["868"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\components\\UserTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\users\\usersApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\settings\\warehousesApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\BackendStatusChecker.tsx", ["869"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\api\\customerApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\CustomerModal.tsx", ["870", "871", "872", "873"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\hooks\\useCustomerData.ts", ["874", "875"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\styles\\customerModalStyles.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\CustomerModal\\utils\\customerHelpers.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableLogic.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\dynamicTable\\DynamicTableStyles.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ErrorToast.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\ItemsTable.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\LoadingDemo.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\itemsTable\\TableActionButtons.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\NetworkStatusChecker.tsx", ["876"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\SideTaskBar\\SideTaskBar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\TopTaskBar\\TopTaskBar.tsx", ["877"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\LogDetailsModal.tsx", ["878", "879"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\logsApi.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\components\\UserModal\\UserModal.tsx", ["880", "881", "882"], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\test-diff\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\entityDisplayConfig.ts", [], [], "C:\\Users\\<USER>\\Documents\\workspace\\projects\\dido-distribution\\frontend\\app\\logs\\components\\examples\\newEntityExample.ts", [], [], {"ruleId": "883", "severity": 1, "message": "884", "line": 255, "column": 6, "nodeType": "885", "endLine": 255, "endColumn": 8, "suggestions": "886"}, {"ruleId": "883", "severity": 1, "message": "887", "line": 78, "column": 6, "nodeType": "885", "endLine": 78, "endColumn": 29, "suggestions": "888", "suppressions": "889"}, {"ruleId": "890", "severity": 2, "message": "891", "line": 100, "column": 50, "nodeType": "892", "messageId": "893", "suggestions": "894"}, {"ruleId": "883", "severity": 1, "message": "895", "line": 228, "column": 6, "nodeType": "885", "endLine": 228, "endColumn": 55, "suggestions": "896"}, {"ruleId": "883", "severity": 1, "message": "897", "line": 433, "column": 6, "nodeType": "885", "endLine": 433, "endColumn": 54, "suggestions": "898"}, {"ruleId": "883", "severity": 1, "message": "899", "line": 433, "column": 7, "nodeType": "900", "endLine": 433, "endColumn": 21}, {"ruleId": "883", "severity": 1, "message": "901", "line": 152, "column": 6, "nodeType": "885", "endLine": 152, "endColumn": 66, "suggestions": "902", "suppressions": "903"}, {"ruleId": "883", "severity": 1, "message": "904", "line": 152, "column": 20, "nodeType": "900", "endLine": 152, "endColumn": 65, "suppressions": "905"}, {"ruleId": "883", "severity": 1, "message": "906", "line": 57, "column": 48, "nodeType": "885", "endLine": 57, "endColumn": 62, "suggestions": "907"}, {"ruleId": "883", "severity": 1, "message": "908", "line": 58, "column": 56, "nodeType": "885", "endLine": 58, "endColumn": 74, "suggestions": "909"}, {"ruleId": "883", "severity": 1, "message": "910", "line": 48, "column": 6, "nodeType": "885", "endLine": 48, "endColumn": 21, "suggestions": "911"}, {"ruleId": "883", "severity": 1, "message": "912", "line": 185, "column": 6, "nodeType": "885", "endLine": 185, "endColumn": 16, "suggestions": "913"}, {"ruleId": "890", "severity": 2, "message": "914", "line": 348, "column": 42, "nodeType": "892", "messageId": "893", "suggestions": "915"}, {"ruleId": "890", "severity": 2, "message": "914", "line": 348, "column": 56, "nodeType": "892", "messageId": "893", "suggestions": "916"}, {"ruleId": "883", "severity": 1, "message": "917", "line": 400, "column": 6, "nodeType": "885", "endLine": 400, "endColumn": 8, "suggestions": "918"}, {"ruleId": "883", "severity": 1, "message": "919", "line": 447, "column": 6, "nodeType": "885", "endLine": 447, "endColumn": 39, "suggestions": "920"}, {"ruleId": "883", "severity": 1, "message": "919", "line": 469, "column": 6, "nodeType": "885", "endLine": 469, "endColumn": 39, "suggestions": "921"}, {"ruleId": "883", "severity": 1, "message": "919", "line": 483, "column": 6, "nodeType": "885", "endLine": 483, "endColumn": 29, "suggestions": "922"}, {"ruleId": "923", "severity": 2, "message": "924", "line": 207, "column": 24, "nodeType": "925", "endLine": 207, "endColumn": 35}, {"ruleId": "923", "severity": 2, "message": "926", "line": 271, "column": 3, "nodeType": "925", "endLine": 271, "endColumn": 12}, {"ruleId": "923", "severity": 2, "message": "926", "line": 280, "column": 3, "nodeType": "925", "endLine": 280, "endColumn": 12}, {"ruleId": "923", "severity": 2, "message": "926", "line": 285, "column": 3, "nodeType": "925", "endLine": 285, "endColumn": 12}, {"ruleId": "923", "severity": 2, "message": "926", "line": 290, "column": 3, "nodeType": "925", "endLine": 290, "endColumn": 12}, {"ruleId": "883", "severity": 1, "message": "927", "line": 359, "column": 6, "nodeType": "885", "endLine": 359, "endColumn": 144, "suggestions": "928"}, {"ruleId": "923", "severity": 2, "message": "926", "line": 362, "column": 3, "nodeType": "925", "endLine": 362, "endColumn": 12}, {"ruleId": "923", "severity": 2, "message": "926", "line": 399, "column": 3, "nodeType": "925", "endLine": 399, "endColumn": 12}, {"ruleId": "923", "severity": 2, "message": "924", "line": 422, "column": 25, "nodeType": "925", "endLine": 422, "endColumn": 36}, {"ruleId": "923", "severity": 2, "message": "926", "line": 440, "column": 3, "nodeType": "925", "endLine": 440, "endColumn": 12}, {"ruleId": "923", "severity": 2, "message": "926", "line": 452, "column": 3, "nodeType": "925", "endLine": 452, "endColumn": 12}, {"ruleId": "923", "severity": 2, "message": "926", "line": 483, "column": 3, "nodeType": "925", "endLine": 483, "endColumn": 12}, {"ruleId": "923", "severity": 2, "message": "926", "line": 491, "column": 3, "nodeType": "925", "endLine": 491, "endColumn": 12}, {"ruleId": "883", "severity": 1, "message": "929", "line": 51, "column": 6, "nodeType": "885", "endLine": 51, "endColumn": 17, "suggestions": "930"}, {"ruleId": "883", "severity": 1, "message": "906", "line": 16, "column": 48, "nodeType": "885", "endLine": 16, "endColumn": 72, "suggestions": "931"}, {"ruleId": "883", "severity": 1, "message": "932", "line": 16, "column": 49, "nodeType": "900", "endLine": 16, "endColumn": 71}, {"ruleId": "883", "severity": 1, "message": "908", "line": 17, "column": 56, "nodeType": "885", "endLine": 17, "endColumn": 84, "suggestions": "933"}, {"ruleId": "883", "severity": 1, "message": "932", "line": 17, "column": 57, "nodeType": "900", "endLine": 17, "endColumn": 83}, {"ruleId": "934", "severity": 1, "message": "935", "line": 127, "column": 22, "nodeType": "936", "endLine": 131, "endColumn": 24}, {"ruleId": "883", "severity": 1, "message": "937", "line": 85, "column": 6, "nodeType": "885", "endLine": 85, "endColumn": 8, "suggestions": "938"}, {"ruleId": "883", "severity": 1, "message": "939", "line": 71, "column": 6, "nodeType": "885", "endLine": 71, "endColumn": 14, "suggestions": "940"}, {"ruleId": "883", "severity": 1, "message": "941", "line": 97, "column": 6, "nodeType": "885", "endLine": 97, "endColumn": 44, "suggestions": "942"}, {"ruleId": "890", "severity": 2, "message": "914", "line": 272, "column": 86, "nodeType": "892", "messageId": "893", "suggestions": "943"}, {"ruleId": "890", "severity": 2, "message": "914", "line": 272, "column": 99, "nodeType": "892", "messageId": "893", "suggestions": "944"}, {"ruleId": "883", "severity": 1, "message": "945", "line": 51, "column": 6, "nodeType": "885", "endLine": 51, "endColumn": 8, "suggestions": "946"}, {"ruleId": "883", "severity": 1, "message": "947", "line": 150, "column": 40, "nodeType": "925", "endLine": 150, "endColumn": 47}, {"ruleId": "883", "severity": 1, "message": "948", "line": 136, "column": 6, "nodeType": "885", "endLine": 136, "endColumn": 8, "suggestions": "949"}, {"ruleId": "883", "severity": 1, "message": "950", "line": 83, "column": 6, "nodeType": "885", "endLine": 83, "endColumn": 60, "suggestions": "951"}, {"ruleId": "890", "severity": 2, "message": "914", "line": 245, "column": 46, "nodeType": "892", "messageId": "893", "suggestions": "952"}, {"ruleId": "890", "severity": 2, "message": "914", "line": 245, "column": 54, "nodeType": "892", "messageId": "893", "suggestions": "953"}, {"ruleId": "883", "severity": 1, "message": "941", "line": 108, "column": 6, "nodeType": "885", "endLine": 108, "endColumn": 49, "suggestions": "954"}, {"ruleId": "890", "severity": 2, "message": "914", "line": 265, "column": 78, "nodeType": "892", "messageId": "893", "suggestions": "955"}, {"ruleId": "890", "severity": 2, "message": "914", "line": 265, "column": 91, "nodeType": "892", "messageId": "893", "suggestions": "956"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'clearAllTimeouts', 'handleAuth', and 'router'. Either include them or remove the dependency array.", "ArrayExpression", ["957"], "React Hook useEffect has missing dependencies: 'checkUserExists' and 'logout'. Either include them or remove the dependency array.", ["958"], ["959"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["960", "961", "962", "963"], "React Hook React.useEffect has a missing dependency: 'previousPaginationData'. Either include it or remove the dependency array.", ["964"], "React Hook React.useEffect has a missing dependency: 'watch'. Either include it or remove the dependency array.", ["965"], "React Hook React.useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "React Hook useEffect has missing dependencies: 'adjustments' and 'setValue'. Either include them or remove the dependency array.", ["966"], ["967"], "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["968"], "React Hook useMemo has a missing dependency: 'filter'. Either include it or remove the dependency array.", ["969"], "React Hook useMemo has a missing dependency: 'pagination'. Either include it or remove the dependency array.", ["970"], "React Hook useEffect has a missing dependency: 'loadInitialData'. Either include it or remove the dependency array.", ["971"], "React Hook useEffect has a missing dependency: 'loadWarehouses'. Either include it or remove the dependency array.", ["972"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["973", "974", "975", "976"], ["977", "978", "979", "980"], "React Hook useEffect has a missing dependency: 'handleAddPayment'. Either include it or remove the dependency array.", ["981"], "React Hook useCallback has a missing dependency: 'actions'. Either include it or remove the dependency array.", ["982"], ["983"], ["984"], "react-hooks/rules-of-hooks", "React Hook \"useCallback\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "Identifier", "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "React Hook useEffect has a missing dependency: 'loadSale'. Either include it or remove the dependency array.", ["985"], "React Hook useEffect has a missing dependency: 'handleNewSale'. Either include it or remove the dependency array.", ["986"], ["987"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["988"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useEffect has missing dependencies: 'checkBackendStatus' and 'checkTimeout'. Either include them or remove the dependency array.", ["989"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["990"], "React Hook useEffect has a missing dependency: 'debouncedSearchTerm'. Either include it or remove the dependency array.", ["991"], ["992", "993", "994", "995"], ["996", "997", "998", "999"], "React Hook useCallback has a missing dependency: 'CACHE_EXPIRY'. Either include it or remove the dependency array.", ["1000"], "The ref value 'requestTimeoutRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'requestTimeoutRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "React Hook useEffect has missing dependencies: 'checkNetworkStatus' and 'checkTimeout'. Either include them or remove the dependency array.", ["1001"], "React Hook React.useEffect has missing dependencies: 'fetchAndPersistWarehouseInfo' and 'user'. Either include them or remove the dependency array.", ["1002"], ["1003", "1004", "1005", "1006"], ["1007", "1008", "1009", "1010"], ["1011"], ["1012", "1013", "1014", "1015"], ["1016", "1017", "1018", "1019"], {"desc": "1020", "fix": "1021"}, {"desc": "1022", "fix": "1023"}, {"kind": "1024", "justification": "1025"}, {"messageId": "1026", "data": "1027", "fix": "1028", "desc": "1029"}, {"messageId": "1026", "data": "1030", "fix": "1031", "desc": "1032"}, {"messageId": "1026", "data": "1033", "fix": "1034", "desc": "1035"}, {"messageId": "1026", "data": "1036", "fix": "1037", "desc": "1038"}, {"desc": "1039", "fix": "1040"}, {"desc": "1041", "fix": "1042"}, {"desc": "1043", "fix": "1044"}, {"kind": "1024", "justification": "1025"}, {"kind": "1024", "justification": "1025"}, {"desc": "1045", "fix": "1046"}, {"desc": "1047", "fix": "1048"}, {"desc": "1049", "fix": "1050"}, {"desc": "1051", "fix": "1052"}, {"messageId": "1026", "data": "1053", "fix": "1054", "desc": "1055"}, {"messageId": "1026", "data": "1056", "fix": "1057", "desc": "1058"}, {"messageId": "1026", "data": "1059", "fix": "1060", "desc": "1061"}, {"messageId": "1026", "data": "1062", "fix": "1063", "desc": "1064"}, {"messageId": "1026", "data": "1065", "fix": "1066", "desc": "1055"}, {"messageId": "1026", "data": "1067", "fix": "1068", "desc": "1058"}, {"messageId": "1026", "data": "1069", "fix": "1070", "desc": "1061"}, {"messageId": "1026", "data": "1071", "fix": "1072", "desc": "1064"}, {"desc": "1073", "fix": "1074"}, {"desc": "1075", "fix": "1076"}, {"desc": "1075", "fix": "1077"}, {"desc": "1078", "fix": "1079"}, {"desc": "1080", "fix": "1081"}, {"desc": "1082", "fix": "1083"}, {"desc": "1045", "fix": "1084"}, {"desc": "1047", "fix": "1085"}, {"desc": "1086", "fix": "1087"}, {"desc": "1088", "fix": "1089"}, {"desc": "1090", "fix": "1091"}, {"messageId": "1026", "data": "1092", "fix": "1093", "desc": "1055"}, {"messageId": "1026", "data": "1094", "fix": "1095", "desc": "1058"}, {"messageId": "1026", "data": "1096", "fix": "1097", "desc": "1061"}, {"messageId": "1026", "data": "1098", "fix": "1099", "desc": "1064"}, {"messageId": "1026", "data": "1100", "fix": "1101", "desc": "1055"}, {"messageId": "1026", "data": "1102", "fix": "1103", "desc": "1058"}, {"messageId": "1026", "data": "1104", "fix": "1105", "desc": "1061"}, {"messageId": "1026", "data": "1106", "fix": "1107", "desc": "1064"}, {"desc": "1108", "fix": "1109"}, {"desc": "1110", "fix": "1111"}, {"desc": "1112", "fix": "1113"}, {"messageId": "1026", "data": "1114", "fix": "1115", "desc": "1055"}, {"messageId": "1026", "data": "1116", "fix": "1117", "desc": "1058"}, {"messageId": "1026", "data": "1118", "fix": "1119", "desc": "1061"}, {"messageId": "1026", "data": "1120", "fix": "1121", "desc": "1064"}, {"messageId": "1026", "data": "1122", "fix": "1123", "desc": "1055"}, {"messageId": "1026", "data": "1124", "fix": "1125", "desc": "1058"}, {"messageId": "1026", "data": "1126", "fix": "1127", "desc": "1061"}, {"messageId": "1026", "data": "1128", "fix": "1129", "desc": "1064"}, {"desc": "1130", "fix": "1131"}, {"messageId": "1026", "data": "1132", "fix": "1133", "desc": "1055"}, {"messageId": "1026", "data": "1134", "fix": "1135", "desc": "1058"}, {"messageId": "1026", "data": "1136", "fix": "1137", "desc": "1061"}, {"messageId": "1026", "data": "1138", "fix": "1139", "desc": "1064"}, {"messageId": "1026", "data": "1140", "fix": "1141", "desc": "1055"}, {"messageId": "1026", "data": "1142", "fix": "1143", "desc": "1058"}, {"messageId": "1026", "data": "1144", "fix": "1145", "desc": "1061"}, {"messageId": "1026", "data": "1146", "fix": "1147", "desc": "1064"}, "Update the dependencies array to be: [clearAllTimeouts, handleAuth, router]", {"range": "1148", "text": "1149"}, "Update the dependencies array to be: [router, user, loading, checkUserExists, logout]", {"range": "1150", "text": "1151"}, "directive", "", "replaceWithAlt", {"alt": "1152"}, {"range": "1153", "text": "1154"}, "Replace with `&apos;`.", {"alt": "1155"}, {"range": "1156", "text": "1157"}, "Replace with `&lsquo;`.", {"alt": "1158"}, {"range": "1159", "text": "1160"}, "Replace with `&#39;`.", {"alt": "1161"}, {"range": "1162", "text": "1163"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [paginatedData, isLoading, currentPage, pageSize, previousPaginationData]", {"range": "1164", "text": "1165"}, "Update the dependencies array to be: [showAdditionalPrices, setValue, watch]", {"range": "1166", "text": "1167"}, "Update the dependencies array to be: [adjustments, setValue, storageUuid]", {"range": "1168", "text": "1169"}, "Update the dependencies array to be: [filter]", {"range": "1170", "text": "1171"}, "Update the dependencies array to be: [pagination]", {"range": "1172", "text": "1173"}, "Update the dependencies array to be: [loadInitialData, warehouseUuid]", {"range": "1174", "text": "1175"}, "Update the dependencies array to be: [loadWarehouses, userUuid]", {"range": "1176", "text": "1177"}, {"alt": "1178"}, {"range": "1179", "text": "1180"}, "Replace with `&quot;`.", {"alt": "1181"}, {"range": "1182", "text": "1183"}, "Replace with `&ldquo;`.", {"alt": "1184"}, {"range": "1185", "text": "1186"}, "Replace with `&#34;`.", {"alt": "1187"}, {"range": "1188", "text": "1189"}, "Replace with `&rdquo;`.", {"alt": "1178"}, {"range": "1190", "text": "1191"}, {"alt": "1181"}, {"range": "1192", "text": "1193"}, {"alt": "1184"}, {"range": "1194", "text": "1195"}, {"alt": "1187"}, {"range": "1196", "text": "1197"}, "Update the dependencies array to be: [handleAddPayment]", {"range": "1198", "text": "1199"}, "Update the dependencies array to be: [actions, userUuid]", {"range": "1200", "text": "1201"}, {"range": "1202", "text": "1201"}, "Update the dependencies array to be: [actions]", {"range": "1203", "text": "1204"}, "Update the dependencies array to be: [editSaleData, loadSaleUuid, warehouseUuid, isEditMode, isLoadMode, saleState, loadSaleForEdit, loadSaleForContinue, setError, setLoading, loadSale]", {"range": "1205", "text": "1206"}, "Update the dependencies array to be: [handleNewSale, onNewSale]", {"range": "1207", "text": "1208"}, {"range": "1209", "text": "1171"}, {"range": "1210", "text": "1173"}, "Update the dependencies array to be: [checkBackendStatus, checkTimeout]", {"range": "1211", "text": "1212"}, "Update the dependencies array to be: [handleClose, isOpen]", {"range": "1213", "text": "1214"}, "Update the dependencies array to be: [isOpen, warehouseUuid, loadCustomers, debouncedSearchTerm]", {"range": "1215", "text": "1216"}, {"alt": "1178"}, {"range": "1217", "text": "1218"}, {"alt": "1181"}, {"range": "1219", "text": "1220"}, {"alt": "1184"}, {"range": "1221", "text": "1222"}, {"alt": "1187"}, {"range": "1223", "text": "1224"}, {"alt": "1178"}, {"range": "1225", "text": "1178"}, {"alt": "1181"}, {"range": "1226", "text": "1181"}, {"alt": "1184"}, {"range": "1227", "text": "1184"}, {"alt": "1187"}, {"range": "1228", "text": "1187"}, "Update the dependencies array to be: [CACHE_EXPIRY]", {"range": "1229", "text": "1230"}, "Update the dependencies array to be: [checkNetworkStatus, checkTimeout]", {"range": "1231", "text": "1232"}, "Update the dependencies array to be: [user?.uuid, user.warehouseUuid, user.warehouseName, user, fetchAndPersistWarehouseInfo]", {"range": "1233", "text": "1234"}, {"alt": "1178"}, {"range": "1235", "text": "1178"}, {"alt": "1181"}, {"range": "1236", "text": "1181"}, {"alt": "1184"}, {"range": "1237", "text": "1184"}, {"alt": "1187"}, {"range": "1238", "text": "1187"}, {"alt": "1178"}, {"range": "1239", "text": "1178"}, {"alt": "1181"}, {"range": "1240", "text": "1181"}, {"alt": "1184"}, {"range": "1241", "text": "1184"}, {"alt": "1187"}, {"range": "1242", "text": "1187"}, "Update the dependencies array to be: [isOpen, effectiveWarehouseUuid, loadUsers, debouncedSearchTerm]", {"range": "1243", "text": "1244"}, {"alt": "1178"}, {"range": "1245", "text": "1246"}, {"alt": "1181"}, {"range": "1247", "text": "1248"}, {"alt": "1184"}, {"range": "1249", "text": "1250"}, {"alt": "1187"}, {"range": "1251", "text": "1252"}, {"alt": "1178"}, {"range": "1253", "text": "1178"}, {"alt": "1181"}, {"range": "1254", "text": "1181"}, {"alt": "1184"}, {"range": "1255", "text": "1184"}, {"alt": "1187"}, {"range": "1256", "text": "1187"}, [10498, 10500], "[clearAllTimeouts, handleAuth, router]", [3802, 3825], "[router, user, loading, checkUserExists, logout]", "&apos;", [3109, 3138], "! Here&apos;s an overview of your ", "&lsquo;", [3109, 3138], "! Here&lsquo;s an overview of your ", "&#39;", [3109, 3138], "! Here&#39;s an overview of your ", "&rsquo;", [3109, 3138], "! Here&rsquo;s an overview of your ", [9126, 9175], "[paginatedData, isLoading, currentPage, pageSize, previousPaginationData]", [17880, 17928], "[showAdditionalPrices, setValue, watch]", [5237, 5297], "[adjustments, setValue, storageUuid]", [1716, 1730], "[filter]", [1789, 1807], "[pagination]", [1692, 1707], "[loadInitialData, warehouseUuid]", [6787, 6797], "[loadWarehouses, userUuid]", "&quot;", [11603, 11705], "\n              No warehouses found. Click &quot;Add Warehouse\" to create your first warehouse.\n            ", "&ldquo;", [11603, 11705], "\n              No warehouses found. Click &ldquo;Add Warehouse\" to create your first warehouse.\n            ", "&#34;", [11603, 11705], "\n              No warehouses found. Click &#34;Add Warehouse\" to create your first warehouse.\n            ", "&rdquo;", [11603, 11705], "\n              No warehouses found. Click &rdquo;Add Warehouse\" to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&quot; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&ldquo; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&#34; to create your first warehouse.\n            ", [11603, 11705], "\n              No warehouses found. Click \"Add Warehouse&rdquo; to create your first warehouse.\n            ", [13806, 13808], "[handleAddPayment]", [15158, 15191], "[actions, userUuid]", [15855, 15888], [16405, 16428], "[actions]", [12933, 13071], "[editSaleData, loadSaleUuid, warehouseUuid, isEditMode, isLoadMode, saleState, loadSaleForEdit, loadSaleForContinue, setError, setLoading, loadSale]", [1358, 1369], "[handleNewSale, onNewSale]", [705, 729], [788, 816], [2758, 2760], "[checkBackendStatus, checkTimeout]", [2182, 2190], "[handleClose, isOpen]", [2880, 2918], "[isOpen, warehouseUuid, loadCustomers, debouncedSearchTerm]", [9605, 9634], "No customers found matching &quot;", [9605, 9634], "No customers found matching &ldquo;", [9605, 9634], "No customers found matching &#34;", [9605, 9634], "No customers found matching &rdquo;", [9646, 9647], [9646, 9647], [9646, 9647], [9646, 9647], [2007, 2009], "[CACHE_EXPIRY]", [4713, 4715], "[checkNetworkStatus, checkTimeout]", [2945, 2999], "[user?.uuid, user.warehouseUuid, user.warehouseName, user, fetchAndPersistWarehouseInfo]", [9326, 9327], [9326, 9327], [9326, 9327], [9326, 9327], [9334, 9335], [9334, 9335], [9334, 9335], [9334, 9335], [3576, 3619], "[isOpen, effectiveWarehouseUuid, loadUsers, debouncedSearchTerm]", [9420, 9445], "No users found matching &quot;", [9420, 9445], "No users found matching &ldquo;", [9420, 9445], "No users found matching &#34;", [9420, 9445], "No users found matching &rdquo;", [9457, 9458], [9457, 9458], [9457, 9458], [9457, 9458]]