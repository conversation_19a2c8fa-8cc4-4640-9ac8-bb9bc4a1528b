import { ApiProperty } from "@nestjs/swagger";
import { GoodsReceipt, GoodsReceiptStatus } from "../goods-receipt.entity";
import { GoodsReceiptItem } from "../goods-receipt-item.entity";

export class GoodsReceiptItemResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the goods receipt item",
  })
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the goods receipt this item belongs to",
  })
  goodsReceiptUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase item this receipt item corresponds to",
  })
  purchaseItemUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  productUuid: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of receipt",
  })
  name: string;

  @ApiProperty({
    example: 5,
    description: "Quantity ordered",
  })
  quantityOrdered: number;

  @ApiProperty({
    example: 4,
    description: "Quantity received",
  })
  quantityReceived: number;

  @ApiProperty({
    example: 1,
    description: "Quantity rejected/damaged",
  })
  quantityRejected: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of purchase",
  })
  unitPrice: number;

  @ApiProperty({
    example: "Good condition",
    description: "Notes about the received items",
  })
  notes?: string;

  @ApiProperty({
    example: 0,
    description: "Order of the item in the receipt",
  })
  order: number;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "When the receipt item was created",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "When the receipt item was last updated",
  })
  updatedAt: Date;
}

export class GoodsReceiptResponseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the goods receipt",
  })
  id: string;

  @ApiProperty({
    example: "GR-20250115-001",
    description: "Unique goods receipt number",
  })
  receiptNumber: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase this receipt is for",
  })
  purchaseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
  })
  supplierUuid: string;

  @ApiProperty({
    example: "Supplier Name",
    description: "Supplier name at time of receipt",
  })
  supplierName: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse where goods are received",
  })
  warehouseUuid: string;

  @ApiProperty({
    example: "Warehouse Name",
    description: "Warehouse name at time of receipt",
  })
  warehouseName: string;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Date when goods were received",
  })
  receiptDate: Date;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Expected receipt date",
  })
  expectedReceiptDate: Date;

  @ApiProperty({
    example: GoodsReceiptStatus.PENDING,
    description: "Current status of the goods receipt",
    enum: GoodsReceiptStatus,
  })
  status: GoodsReceiptStatus;

  @ApiProperty({
    example: "Goods received in good condition",
    description: "Notes about the receipt",
  })
  notes?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the receipt",
  })
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the receipt",
  })
  updatedBy: string;

  @ApiProperty({
    example: false,
    description: "Whether the receipt has been soft deleted",
  })
  isDeleted: boolean;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "When the receipt was created",
  })
  createdAt: Date;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "When the receipt was last updated",
  })
  updatedAt: Date;

  @ApiProperty({
    type: [GoodsReceiptItemResponseDto],
    description: "Array of goods receipt items",
  })
  receiptItems: GoodsReceiptItemResponseDto[];
}

export class GoodsReceiptListResponseDto {
  @ApiProperty({
    type: [GoodsReceiptResponseDto],
    description: "Array of goods receipts",
  })
  receipts: GoodsReceiptResponseDto[];

  @ApiProperty({
    example: 100,
    description: "Total number of receipts",
  })
  total: number;

  @ApiProperty({
    example: 1,
    description: "Current page number",
  })
  page: number;

  @ApiProperty({
    example: 10,
    description: "Number of receipts per page",
  })
  limit: number;

  @ApiProperty({
    example: 10,
    description: "Total number of pages",
  })
  totalPages: number;
}

export function toGoodsReceiptItemResponseDto(item: GoodsReceiptItem): GoodsReceiptItemResponseDto {
  return {
    id: item.id,
    goodsReceiptUuid: item.goodsReceiptUuid,
    purchaseItemUuid: item.purchaseItemUuid,
    productUuid: item.productUuid,
    name: item.name,
    quantityOrdered: item.quantityOrdered,
    quantityReceived: item.quantityReceived,
    quantityRejected: item.quantityRejected,
    unitPrice: item.unitPrice,
    notes: item.notes,
    order: item.order,
    createdAt: item.createdAt,
    updatedAt: item.updatedAt,
  };
}

export function toGoodsReceiptResponseDto(receipt: GoodsReceipt, items?: GoodsReceiptItem[]): GoodsReceiptResponseDto {
  return {
    id: receipt.id,
    receiptNumber: receipt.receiptNumber,
    purchaseUuid: receipt.purchaseUuid,
    supplierUuid: receipt.supplierUuid,
    supplierName: receipt.supplierName,
    warehouseUuid: receipt.warehouseUuid,
    warehouseName: receipt.warehouseName,
    receiptDate: receipt.receiptDate,
    expectedReceiptDate: receipt.expectedReceiptDate,
    status: receipt.status,
    notes: receipt.notes,
    createdBy: receipt.createdBy,
    updatedBy: receipt.updatedBy,
    isDeleted: receipt.isDeleted,
    createdAt: receipt.createdAt,
    updatedAt: receipt.updatedAt,
    receiptItems: items ? items.map(toGoodsReceiptItemResponseDto) : [],
  };
}

export function toGoodsReceiptResponseDtoArray(receipts: GoodsReceipt[], itemsMap?: Map<string, GoodsReceiptItem[]>): GoodsReceiptResponseDto[] {
  return receipts.map(receipt => {
    const items = itemsMap ? itemsMap.get(receipt.id) : undefined;
    return toGoodsReceiptResponseDto(receipt, items);
  });
} 