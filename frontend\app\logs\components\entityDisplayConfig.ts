import type { Log } from '../logsApi';

export interface EntityDisplayConfig {
  getDisplayName: (log: Log) => string;
  getSubtitle: (log: Log) => string;
  getIcon: () => string;
  getColor: () => string;
  getDescription?: (log: Log) => string;
  getMetadata?: (log: Log) => Array<{ label: string; value: string; color?: string }>;
}

export const entityDisplayConfigs: Record<string, EntityDisplayConfig> = {
  // Sales entities
  sale: {
    getDisplayName: (log: Log) => log.data?.invoiceNumber || 'Sale',
    getSubtitle: (log: Log) => log.entity.replace('sale_', ''),
    getIcon: () => '🧾',
    getColor: () => 'text-blue-600',
    getDescription: (log: Log) => {
      const customer = log.data?.customerName;
      const total = log.data?.totalAmount;
      if (customer && total) {
        return `Sale to ${customer} - $${total}`;
      }
      if (customer) return `Sale to ${customer}`;
      if (total) return `Sale total: $${total}`;
      return undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.customerName) {
        metadata.push({ label: 'Customer', value: log.data.customerName, color: 'text-blue-600' });
      }
      if (log.data?.totalAmount) {
        metadata.push({ label: 'Total', value: `$${log.data.totalAmount}`, color: 'text-green-600' });
      }
      if (log.data?.status) {
        metadata.push({ label: 'Status', value: log.data.status, color: 'text-purple-600' });
      }
      return metadata;
    },
  },

  // Purchase entities
  purchase: {
    getDisplayName: (log: Log) => log.data?.purchaseNumber || log.data?.referenceNumber || 'Purchase',
    getSubtitle: (log: Log) => log.entity.replace('purchase_', ''),
    getIcon: () => '📦',
    getColor: () => 'text-green-600',
    getDescription: (log: Log) => {
      const supplier = log.data?.supplierName;
      const total = log.data?.totalAmount;
      if (supplier && total) {
        return `Purchase from ${supplier} - $${total}`;
      }
      if (supplier) return `Purchase from ${supplier}`;
      if (total) return `Purchase total: $${total}`;
      return undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.supplierName) {
        metadata.push({ label: 'Supplier', value: log.data.supplierName, color: 'text-green-600' });
      }
      if (log.data?.totalAmount) {
        metadata.push({ label: 'Total', value: `$${log.data.totalAmount}`, color: 'text-green-600' });
      }
      if (log.data?.status) {
        metadata.push({ label: 'Status', value: log.data.status, color: 'text-purple-600' });
      }
      return metadata;
    },
  },

  // Product entities
  product: {
    getDisplayName: (log: Log) => log.data?.productName || log.data?.name || 'Product',
    getSubtitle: (log: Log) => log.entity.replace('product_', ''),
    getIcon: () => '📱',
    getColor: () => 'text-purple-600',
    getDescription: (log: Log) => {
      const category = log.data?.categoryName;
      const price = log.data?.price || log.data?.unitPrice;
      if (category && price) {
        return `${category} - $${price}`;
      }
      if (category) return `Category: ${category}`;
      if (price) return `Price: $${price}`;
      return undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.categoryName) {
        metadata.push({ label: 'Category', value: log.data.categoryName, color: 'text-purple-600' });
      }
      if (log.data?.price || log.data?.unitPrice) {
        const price = log.data.price || log.data.unitPrice;
        metadata.push({ label: 'Price', value: `$${price}`, color: 'text-green-600' });
      }
      if (log.data?.sku) {
        metadata.push({ label: 'SKU', value: log.data.sku, color: 'text-gray-600' });
      }
      return metadata;
    },
  },

  // Customer entities
  customer: {
    getDisplayName: (log: Log) => log.data?.customerName || log.data?.name || 'Customer',
    getSubtitle: (log: Log) => log.entity.replace('customer_', ''),
    getIcon: () => '👤',
    getColor: () => 'text-indigo-600',
    getDescription: (log: Log) => {
      const email = log.data?.email;
      const phone = log.data?.phone;
      if (email && phone) {
        return `${email} • ${phone}`;
      }
      if (email) return email;
      if (phone) return phone;
      return undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.email) {
        metadata.push({ label: 'Email', value: log.data.email, color: 'text-indigo-600' });
      }
      if (log.data?.phone) {
        metadata.push({ label: 'Phone', value: log.data.phone, color: 'text-indigo-600' });
      }
      if (log.data?.creditLimit) {
        metadata.push({ label: 'Credit Limit', value: `$${log.data.creditLimit}`, color: 'text-green-600' });
      }
      return metadata;
    },
  },

  // Warehouse entities
  warehouse: {
    getDisplayName: (log: Log) => log.data?.warehouseName || log.data?.name || 'Warehouse',
    getSubtitle: (log: Log) => log.entity.replace('warehouse_', ''),
    getIcon: () => '🏢',
    getColor: () => 'text-orange-600',
    getDescription: (log: Log) => {
      const location = log.data?.location || log.data?.address;
      const type = log.data?.type;
      if (location && type) {
        return `${type} - ${location}`;
      }
      if (location) return location;
      if (type) return `Type: ${type}`;
      return undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.location || log.data?.address) {
        const location = log.data.location || log.data.address;
        metadata.push({ label: 'Location', value: location, color: 'text-orange-600' });
      }
      if (log.data?.type) {
        metadata.push({ label: 'Type', value: log.data.type, color: 'text-orange-600' });
      }
      if (log.data?.capacity) {
        metadata.push({ label: 'Capacity', value: log.data.capacity, color: 'text-blue-600' });
      }
      return metadata;
    },
  },

  // User entities
  user: {
    getDisplayName: (log: Log) => log.data?.userName || log.data?.name || 'User',
    getSubtitle: (log: Log) => log.entity.replace('user_', ''),
    getIcon: () => '👨‍💼',
    getColor: () => 'text-gray-600',
    getDescription: (log: Log) => {
      const email = log.data?.email;
      const role = log.data?.role;
      if (email && role) {
        return `${role} - ${email}`;
      }
      if (email) return email;
      if (role) return `Role: ${role}`;
      return undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.email) {
        metadata.push({ label: 'Email', value: log.data.email, color: 'text-gray-600' });
      }
      if (log.data?.role) {
        metadata.push({ label: 'Role', value: log.data.role, color: 'text-purple-600' });
      }
      if (log.data?.department) {
        metadata.push({ label: 'Department', value: log.data.department, color: 'text-blue-600' });
      }
      return metadata;
    },
  },

  // Inventory entities
  inventory: {
    getDisplayName: (log: Log) => log.data?.productName || 'Inventory Item',
    getSubtitle: (log: Log) => log.entity.replace('inventory_', ''),
    getIcon: () => '📊',
    getColor: () => 'text-teal-600',
    getDescription: (log: Log) => {
      const warehouse = log.data?.warehouseName;
      const quantity = log.data?.quantity;
      if (warehouse && quantity !== undefined) {
        return `${warehouse} - Qty: ${quantity}`;
      }
      if (warehouse) return `Warehouse: ${warehouse}`;
      if (quantity !== undefined) return `Quantity: ${quantity}`;
      return undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.warehouseName) {
        metadata.push({ label: 'Warehouse', value: log.data.warehouseName, color: 'text-orange-600' });
      }
      if (log.data?.quantity !== undefined) {
        metadata.push({ label: 'Quantity', value: log.data.quantity.toString(), color: 'text-blue-600' });
      }
      if (log.data?.unitCost) {
        metadata.push({ label: 'Unit Cost', value: `$${log.data.unitCost}`, color: 'text-green-600' });
      }
      return metadata;
    },
  },

  // Supplier entities
  supplier: {
    getDisplayName: (log: Log) => log.data?.supplierName || log.data?.name || 'Supplier',
    getSubtitle: (log: Log) => log.entity.replace('supplier_', ''),
    getIcon: () => '🏭',
    getColor: () => 'text-cyan-600',
    getDescription: (log: Log) => {
      const contact = log.data?.contactPerson;
      const email = log.data?.email;
      if (contact && email) {
        return `Contact: ${contact} - ${email}`;
      }
      if (contact) return `Contact: ${contact}`;
      if (email) return email;
      return undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.contactPerson) {
        metadata.push({ label: 'Contact', value: log.data.contactPerson, color: 'text-cyan-600' });
      }
      if (log.data?.email) {
        metadata.push({ label: 'Email', value: log.data.email, color: 'text-cyan-600' });
      }
      if (log.data?.paymentTerms) {
        metadata.push({ label: 'Payment Terms', value: log.data.paymentTerms, color: 'text-green-600' });
      }
      return metadata;
    },
  },

  // Order entities
  order: {
    getDisplayName: (log: Log) => log.data?.orderNumber || 'Order',
    getSubtitle: (log: Log) => log.entity.replace('order_', ''),
    getIcon: () => '📋',
    getColor: () => 'text-amber-600',
    getDescription: (log: Log) => {
      const customer = log.data?.customerName;
      const status = log.data?.status;
      const total = log.data?.totalAmount;
      const parts = [];
      if (customer) parts.push(customer);
      if (status) parts.push(status);
      if (total) parts.push(`$${total}`);
      return parts.length > 0 ? parts.join(' • ') : undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.customerName) {
        metadata.push({ label: 'Customer', value: log.data.customerName, color: 'text-indigo-600' });
      }
      if (log.data?.status) {
        metadata.push({ label: 'Status', value: log.data.status, color: 'text-purple-600' });
      }
      if (log.data?.totalAmount) {
        metadata.push({ label: 'Total', value: `$${log.data.totalAmount}`, color: 'text-green-600' });
      }
      return metadata;
    },
  },

  // Payment entities
  payment: {
    getDisplayName: (log: Log) => log.data?.paymentNumber || 'Payment',
    getSubtitle: (log: Log) => log.entity.replace('payment_', ''),
    getIcon: () => '💳',
    getColor: () => 'text-emerald-600',
    getDescription: (log: Log) => {
      const amount = log.data?.amount;
      const method = log.data?.paymentMethod;
      const customer = log.data?.customerName;
      const parts = [];
      if (amount) parts.push(`$${amount}`);
      if (method) parts.push(method);
      if (customer) parts.push(customer);
      return parts.length > 0 ? parts.join(' • ') : undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.amount) {
        metadata.push({ label: 'Amount', value: `$${log.data.amount}`, color: 'text-green-600' });
      }
      if (log.data?.paymentMethod) {
        metadata.push({ label: 'Method', value: log.data.paymentMethod, color: 'text-blue-600' });
      }
      if (log.data?.customerName) {
        metadata.push({ label: 'Customer', value: log.data.customerName, color: 'text-indigo-600' });
      }
      return metadata;
    },
  },

  // Route entities
  route: {
    getDisplayName: (log: Log) => log.data?.routeName || log.data?.name || 'Route',
    getSubtitle: (log: Log) => log.entity.replace('route_', ''),
    getIcon: () => '🛣️',
    getColor: () => 'text-rose-600',
    getDescription: (log: Log) => {
      const driver = log.data?.driverName;
      const van = log.data?.vanName;
      const stops = log.data?.stopCount;
      const parts = [];
      if (driver) parts.push(`Driver: ${driver}`);
      if (van) parts.push(`Van: ${van}`);
      if (stops) parts.push(`${stops} stops`);
      return parts.length > 0 ? parts.join(' • ') : undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.driverName) {
        metadata.push({ label: 'Driver', value: log.data.driverName, color: 'text-gray-600' });
      }
      if (log.data?.vanName) {
        metadata.push({ label: 'Van', value: log.data.vanName, color: 'text-blue-600' });
      }
      if (log.data?.stopCount) {
        metadata.push({ label: 'Stops', value: log.data.stopCount.toString(), color: 'text-purple-600' });
      }
      return metadata;
    },
  },

  // Van entities
  van: {
    getDisplayName: (log: Log) => log.data?.vanName || log.data?.name || 'Van',
    getSubtitle: (log: Log) => log.entity.replace('van_', ''),
    getIcon: () => '🚐',
    getColor: () => 'text-sky-600',
    getDescription: (log: Log) => {
      const license = log.data?.licensePlate;
      const driver = log.data?.driverName;
      const capacity = log.data?.capacity;
      const parts = [];
      if (license) parts.push(license);
      if (driver) parts.push(`Driver: ${driver}`);
      if (capacity) parts.push(`Capacity: ${capacity}`);
      return parts.length > 0 ? parts.join(' • ') : undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.licensePlate) {
        metadata.push({ label: 'License', value: log.data.licensePlate, color: 'text-sky-600' });
      }
      if (log.data?.driverName) {
        metadata.push({ label: 'Driver', value: log.data.driverName, color: 'text-gray-600' });
      }
      if (log.data?.capacity) {
        metadata.push({ label: 'Capacity', value: log.data.capacity, color: 'text-blue-600' });
      }
      return metadata;
    },
  },

  // Default fallback for unknown entity types
  default: {
    getDisplayName: (log: Log) => log.data?.name || log.entity,
    getSubtitle: () => '',
    getIcon: () => '📄',
    getColor: () => 'text-gray-600',
    getDescription: (log: Log) => {
      // Try to find a meaningful description from common fields
      const description = log.data?.description || log.data?.notes;
      if (description) return description;
      
      // Try to build a description from common fields
      const name = log.data?.name;
      const type = log.data?.type;
      const status = log.data?.status;
      
      const parts = [];
      if (type) parts.push(`Type: ${type}`);
      if (status) parts.push(`Status: ${status}`);
      
      return parts.length > 0 ? parts.join(' • ') : undefined;
    },
    getMetadata: (log: Log) => {
      const metadata = [];
      if (log.data?.type) {
        metadata.push({ label: 'Type', value: log.data.type, color: 'text-gray-600' });
      }
      if (log.data?.status) {
        metadata.push({ label: 'Status', value: log.data.status, color: 'text-purple-600' });
      }
      return metadata;
    },
  },
};

// Get entity type from entity string (e.g., 'sale_123' -> 'sale')
export const getEntityType = (entity: string): string => {
  const parts = entity.split('_');
  return parts[0] || 'default';
};

// Get entity display configuration
export const getEntityConfig = (log: Log): EntityDisplayConfig => {
  const entityType = getEntityType(log.entity);
  return entityDisplayConfigs[entityType] || entityDisplayConfigs.default;
};
