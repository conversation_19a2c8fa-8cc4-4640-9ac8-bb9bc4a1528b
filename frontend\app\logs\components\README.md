# Entity Display Configuration for Logs

This directory contains the configuration system for displaying different entity types in the logs interface. The system uses a strategy pattern to provide customized display logic for each entity type while maintaining a clean, extensible architecture.

## Overview

The logs system can handle any entity type generically, but provides enhanced display capabilities for specific entity types through the `entityDisplayConfig.ts` file.

## Architecture

### Core Components

1. **`entityDisplayConfig.ts`** - Contains all entity-specific display configurations
2. **`LogDetailsModal.tsx`** - Uses the configurations to render enhanced entity details
3. **`page.tsx`** - Uses the configurations for better table display

### Entity Display Configuration Interface

```typescript
interface EntityDisplayConfig {
  getDisplayName: (log: Log) => string;     // Primary display name
  getSubtitle: (log: Log) => string;        // Secondary text (usually entity ID)
  getIcon: () => string;                    // Emoji icon for the entity type
  getColor: () => string;                   // Tailwind CSS color class
  getDescription?: (log: Log) => string;    // Optional description text
  getMetadata?: (log: Log) => Array<{       // Optional metadata badges
    label: string; 
    value: string; 
    color?: string;
  }>;
}
```

## Adding New Entity Types

To add support for a new entity type, simply add a new configuration to the `entityDisplayConfigs` object in `entityDisplayConfig.ts`:

### Example: Adding a "Region" Entity

```typescript
// In entityDisplayConfigs object
region: {
  getDisplayName: (log: Log) => log.data?.regionName || log.data?.name || 'Region',
  getSubtitle: (log: Log) => log.entity.replace('region_', ''),
  getIcon: () => '🗺️',
  getColor: () => 'text-violet-600',
  getDescription: (log: Log) => {
    const manager = log.data?.managerName;
    const area = log.data?.area;
    if (manager && area) {
      return `Manager: ${manager} • Area: ${area}`;
    }
    if (manager) return `Manager: ${manager}`;
    if (area) return `Area: ${area}`;
    return undefined;
  },
  getMetadata: (log: Log) => {
    const metadata = [];
    if (log.data?.managerName) {
      metadata.push({ label: 'Manager', value: log.data.managerName, color: 'text-gray-600' });
    }
    if (log.data?.area) {
      metadata.push({ label: 'Area', value: log.data.area, color: 'text-violet-600' });
    }
    if (log.data?.customerCount) {
      metadata.push({ label: 'Customers', value: log.data.customerCount.toString(), color: 'text-blue-600' });
    }
    return metadata;
  },
},
```

### Steps to Add a New Entity Type

1. **Identify the entity type** - This is the prefix before the underscore in the entity field (e.g., `sale_123` → `sale`)

2. **Add configuration** - Add a new entry to `entityDisplayConfigs` in `entityDisplayConfig.ts`

3. **Choose an appropriate icon** - Use an emoji that represents the entity type

4. **Select a color** - Use a Tailwind CSS color class that fits your design system

5. **Implement the functions**:
   - `getDisplayName`: Return the most meaningful name for the entity
   - `getSubtitle`: Usually the entity ID without the prefix
   - `getDescription`: Optional contextual information
   - `getMetadata`: Optional key-value pairs for additional details

6. **Test the display** - Create a log entry for your entity type and verify it displays correctly

## Existing Entity Types

The system currently supports these entity types:

- **sale** 🧾 - Sales transactions with invoice numbers, customer info, totals
- **purchase** 📦 - Purchase orders with supplier info, totals
- **product** 📱 - Products with categories, prices, SKUs
- **customer** 👤 - Customers with contact info, credit limits
- **warehouse** 🏢 - Warehouses with locations, types, capacity
- **user** 👨‍💼 - Users with roles, departments, contact info
- **inventory** 📊 - Inventory items with quantities, costs
- **supplier** 🏭 - Suppliers with contact info, payment terms
- **order** 📋 - Orders with customer info, status, totals
- **payment** 💳 - Payments with amounts, methods, customers
- **route** 🛣️ - Routes with drivers, vans, stop counts
- **van** 🚐 - Vans with license plates, drivers, capacity

## Fallback Behavior

If an entity type doesn't have a specific configuration, the system falls back to the `default` configuration, which provides generic display logic that works with common field names like `name`, `type`, and `status`.

## Best Practices

1. **Use meaningful icons** - Choose emojis that clearly represent the entity type
2. **Consistent color scheme** - Use colors that fit your overall design system
3. **Prioritize important information** - Put the most relevant data in `getDisplayName` and `getDescription`
4. **Handle missing data gracefully** - Always provide fallbacks for when expected data fields are missing
5. **Keep metadata concise** - Only show the most important 2-4 metadata items to avoid clutter

## Benefits of This Approach

- **Extensible**: Easy to add new entity types without modifying core logic
- **Maintainable**: All entity-specific logic is centralized in one file
- **Consistent**: All entity types follow the same display pattern
- **Flexible**: Each entity type can have completely custom display logic
- **Backward Compatible**: Unknown entity types automatically get generic display
- **Type Safe**: Full TypeScript support with proper interfaces

## Future Enhancements

Potential improvements to consider:

- **Dynamic configuration**: Load entity configs from a backend API
- **User customization**: Allow users to customize how entities are displayed
- **Conditional display**: Show different information based on the operation type
- **Rich formatting**: Support for more complex display elements beyond text and badges
- **Localization**: Support for multiple languages in entity display
