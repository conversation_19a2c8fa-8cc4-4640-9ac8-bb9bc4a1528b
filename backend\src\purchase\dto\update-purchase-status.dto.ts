import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsUUID } from "class-validator";
import { PurchaseStatus } from "../purchase.entity";

export class UpdatePurchaseStatusDto {
  @ApiProperty({
    example: PurchaseStatus.UNPAID,
    description: "Current status of the purchase",
    enum: PurchaseStatus,
  })
  @IsEnum(PurchaseStatus)
  oldStatus: PurchaseStatus;

  @ApiProperty({
    example: PurchaseStatus.PAID,
    description: "New status for the purchase",
    enum: PurchaseStatus,
  })
  @IsEnum(PurchaseStatus)
  newStatus: PurchaseStatus;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user updating the status",
  })
  @IsUUID("all")
  userUuid: string;
} 