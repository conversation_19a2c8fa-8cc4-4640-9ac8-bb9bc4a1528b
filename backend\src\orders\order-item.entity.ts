import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { Order } from './order.entity';

@Entity('order_items')
export class OrderItem {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the order item (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the order this item belongs to",
  })
  @Column('uuid')
  @Index()
  orderUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @Column('uuid')
  @Index()
  productUuid: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of order",
  })
  @Column()
  name: string;

  @ApiProperty({
    example: 5,
    description: "Quantity ordered",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  quantity: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of order",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  unitPrice: number;

  @ApiProperty({
    example: 129.95,
    description: "Line total (quantity * unit price)",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  lineTotal: number;

  @ApiProperty({
    example: 5.25,
    description: "Tax amount for this line item",
    required: false,
  })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @ApiProperty({
    example: "Special instructions for this item",
    description: "Item-specific notes",
    required: false,
  })
  @Column({ nullable: true })
  notes?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to Order
  @ManyToOne(() => Order, order => order.id)
  @JoinColumn({ name: 'orderUuid' })
  order: Order;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 