# TypeORM Migration Workflow with Automated Backup

This document describes the complete migration workflow for the Dido Distribution backend, including automated backup creation and rollback capabilities using the YugabyteDB Management API.

## Overview

The migration system provides:
- **Automatic backups** before each migration via API
- **Safe migration execution** with rollback capabilities
- **Migration status tracking**
- **Database restoration** from backups via API
- **Centralized backup management** through the backup backend

## Quick Reference - Backup Restoration

### List Available Backups
```bash
curl -X GET http://localhost:5000/backup/list
```

### Restore from Latest Backup
```bash
# First, get the latest backup name
curl -X GET http://localhost:5000/backup/list | jq -r '.data[0].name' | xargs -I {} curl -X POST http://localhost:5000/backup/restore -H "Content-Type: application/json" -d '{"backup_name": "{}"}'
```

### Restore from Specific Backup by Name
```bash
curl -X POST http://localhost:5000/backup/restore \
  -H "Content-Type: application/json" \
  -d '{"backup_name": "db-backup_20231201_143022.sql"}'
```

**Example:**
```bash
# List backups first
curl -X GET http://localhost:5000/backup/list

# Restore specific backup
curl -X POST http://localhost:5000/backup/restore \
  -H "Content-Type: application/json" \
  -d '{"backup_name": "db-backup_20231201_143022.sql"}'
```

## Prerequisites

1. **Backup Backend Running**: Ensure the YugabyteDB Management API is running on `http://localhost:5000`
2. **Environment Variables**: Configure your `.env` file with database credentials
3. **Network Access**: Ensure the backup backend is accessible from your development environment

## Backup Backend API Integration

The migration system now uses the centralized YugabyteDB Management API for all backup operations:

### Backup Backend Endpoints
- **Health Check**: `GET http://localhost:5000/health/`
- **Create Backup**: `POST http://localhost:5000/backup/`
- **List Backups**: `GET http://localhost:5000/backup/list`
- **Restore Backup**: `POST http://localhost:5000/backup/restore`
- **Delete Backup**: `DELETE http://localhost:5000/backup/{backup_name}`

### API Authentication
The backup backend uses the same database credentials as your main application, configured through environment variables.

## Quick Start

### 1. Generate a Migration
When you make changes to your entities, generate a migration:

```bash
npm run migration:generate -- -n AddNewColumnToUsers
```

### 2. Run Migrations
Run migrations with automatic backup via API:

```bash
npm run migration:run
```

### 3. Check Migration Status
View current migration status:

```bash
npm run migration:status
```

### 4. Revert if Needed
Revert the last migration:

```bash
npm run migration:revert
```

## Detailed Workflow

### Step 1: Schema Changes
1. Modify your entity files in `src/**/*.entity.ts`
2. Add new entities or modify existing ones
3. Update relationships, columns, or constraints

### Step 2: Generate Migration
```bash
npm run migration:generate -- -n DescriptiveMigrationName
```

This creates a new migration file in `migration/` with:
- **Up migration**: Changes to apply
- **Down migration**: Changes to revert

### Step 3: Review Migration
1. Check the generated migration file
2. Verify the SQL changes are correct
3. Test the migration in development

### Step 4: Run Migration with Backup
```bash
npm run migration:run
```

This script:
1. ✅ Creates a timestamped backup via API
2. ✅ Checks for pending migrations
3. ✅ Runs the migrations
4. ✅ Provides rollback instructions if needed

### Step 4.5: Check Available Backups (Optional)
```bash
curl -X GET http://localhost:5000/backup/list
```

This shows all available backups with:
- Creation date and time
- File size
- Backup status
- Instructions for restoration

### Step 5: Verify Changes
1. Check the application works correctly
2. Verify data integrity
3. Test affected functionality

## Available Commands

### NPM Scripts
```bash
# Generate migration from entity changes
npm run migration:generate -- -n MigrationName

# Run migrations with automatic backup (default)
npm run migration:run

# Revert last migration with backup (default)
npm run migration:revert

# Show migration status (with database connection)
npm run migration:status

# Unsafe commands (use only in development)
npm run migration:run-unsafe
npm run migration:revert-unsafe
```

### Migration Generation vs Manual Creation

**`migration:generate` (Recommended)**:
- Automatically generates migrations from entity changes
- Compares current database schema with entity definitions
- Creates both `up` and `down` migrations automatically
- Use this for most schema changes (adding columns, tables, relationships)

**Manual Migration Creation (Not Available)**:
- The `migration:create` command has been removed as it's rarely needed
- Most migrations can be auto-generated from entity changes
- For complex custom migrations, modify the generated migration file directly

### Migration Commands (Safe by Default)
```bash
# Run migrations with automatic backup
npm run migration:run

# Revert last migration with backup
npm run migration:revert

# Show migration status (with database connection)
npm run migration:status
```

### Backup API Commands
```bash
# Check backup backend health
curl -X GET http://localhost:5000/health/

# Create backup
curl -X POST http://localhost:5000/backup/ \
  -H "Content-Type: application/json" \
  -d '{"prefix": "db-backup"}'

# List all available backups
curl -X GET http://localhost:5000/backup/list

# Restore from specific backup
curl -X POST http://localhost:5000/backup/restore \
  -H "Content-Type: application/json" \
  -d '{"backup_name": "db-backup_20231201_143022.sql"}'

# Delete specific backup
curl -X DELETE http://localhost:5000/backup/db-backup_20231201_143022.sql

# Get system status
curl -X GET http://localhost:5000/health/status

# Test database connection
curl -X GET http://localhost:5000/monitoring/connection
```

## Backup System

### Automatic Backups
- Created before each migration via API call
- Stored centrally in the backup backend
- Named with timestamp for easy identification
- Prefix: "db-backup" for migration-related backups

### Manual Backups
```bash
# Create backup using API
curl -X POST http://localhost:5000/backup/ \
  -H "Content-Type: application/json" \
  -d '{"prefix": "manual"}'

# List available backups
curl -X GET http://localhost:5000/backup/list

# Restore specific backup
curl -X POST http://localhost:5000/backup/restore \
  -H "Content-Type: application/json" \
  -d '{"backup_name": "manual_20231201_143022.sql"}'
```

### Backup Locations
- **Centralized Storage**: Managed by the backup backend
- **File Format**: `{prefix}_{timestamp}.sql`
- **Access**: Via API endpoints

### Backup Restoration

#### List Available Backups
```bash
curl -X GET http://localhost:5000/backup/list
```

This shows all available backups with:
- Creation date and time
- File size
- Backup status
- Instructions for restoration

#### Restore from Latest Backup
```bash
# Get the latest backup name and restore
curl -X GET http://localhost:5000/backup/list | jq -r '.data[0].name' | xargs -I {} curl -X POST http://localhost:5000/backup/restore -H "Content-Type: application/json" -d '{"backup_name": "{}"}'
```

#### Restore from Specific Backup by Name
```bash
curl -X POST http://localhost:5000/backup/restore \
  -H "Content-Type: application/json" \
  -d '{"backup_name": "migration_20231201_143022.sql"}'
```

**Example:**
```bash
# List available backups first
curl -X GET http://localhost:5000/backup/list

# Restore from a specific backup
curl -X POST http://localhost:5000/backup/restore \
  -H "Content-Type: application/json" \
  -d '{"backup_name": "db-backup_20231201_143022.sql"}'
```

**Important Notes:**
- The backup name is the filename (e.g., `db-backup_20231201_143022.sql`)
- This will overwrite the current database
- The API will handle all compatibility issues automatically
- No manual encoding fixes needed

## Rollback Strategy

### Option 1: TypeORM Revert
```bash
npm run migration:revert
```
- Reverts the last migration
- Creates backup before revert via API
- Maintains migration history

### Option 2: Database Restore via API
```bash
# Get latest backup and restore
curl -X GET http://localhost:5000/backup/list | jq -r '.data[0].name' | xargs -I {} curl -X POST http://localhost:5000/backup/restore -H "Content-Type: application/json" -d '{"backup_name": "{}"}'
```
- Restores entire database from backup
- Loses migration history
- Use when migration revert fails

### Option 3: Manual Restore via API
```bash
curl -X POST http://localhost:5000/backup/restore \
  -H "Content-Type: application/json" \
  -d '{"backup_name": "specific_backup_name.sql"}'
```
- Choose specific backup to restore
- More control over which backup to use

## Migration Best Practices

### 1. Always Test First
- Test migrations in development environment
- Verify data integrity after migration
- Check application functionality

### 2. Use Descriptive Names
```bash
# Good
npm run migration:generate -- -n AddEmailColumnToUsers

# Bad
npm run migration:generate -- -n Update1
```

### 3. Review Generated SQL
- Check the generated migration file
- Ensure SQL changes are correct
- Modify if needed before running

### 4. Backup Before Production
- Always create manual backup before production migrations
- Test migration on production-like data
- Have rollback plan ready

### 5. Monitor Migration Status
```bash
npm run migration:status
```

### 6. Monitor Backup Backend Health
```bash
curl -X GET http://localhost:5000/health/
```

## Troubleshooting

### Migration Fails
1. Check the error message
2. Restore from backup via API: 
   ```bash
   curl -X GET http://localhost:5000/backup/list | jq -r '.data[0].name' | xargs -I {} curl -X POST http://localhost:5000/backup/restore -H "Content-Type: application/json" -d '{"backup_name": "{}"}'
   ```
3. Or restore from specific backup:
   ```bash
   curl -X POST http://localhost:5000/backup/restore \
     -H "Content-Type: application/json" \
     -d '{"backup_name": "specific_backup_name.sql"}'
   ```
4. Fix the migration file
5. Re-run the migration

### Backup Backend Issues
1. Check if backup backend is running: `curl -X GET http://localhost:5000/health/`
2. Verify backup backend configuration
3. Check network connectivity to backup backend
4. Review backup backend logs

### TypeORM Commands Fail
1. Verify `data-source.ts` configuration
2. Check entity paths are correct
3. Ensure TypeORM CLI is installed
4. Verify database connection

### Environment Issues
1. Check `.env` file exists and is readable
2. Verify all required variables are set
3. Ensure backup backend can access the database

### API Connection Issues
If you encounter connection issues with the backup backend:

**Problem**: Backup backend is not accessible or not running.

**Solutions**:

1. **Check backup backend status**:
   ```bash
   curl -X GET http://localhost:5000/health/
   ```

2. **Verify backup backend is running**:
   ```bash
   # Check if the service is running
   curl -X GET http://localhost:5000/health/status
   ```

3. **Test database connection via API**:
   ```bash
   curl -X GET http://localhost:5000/monitoring/connection
   ```

4. **Check system resources**:
   ```bash
   curl -X GET http://localhost:5000/monitoring/system
   ```

5. **Manual backup creation**:
   ```bash
   curl -X POST http://localhost:5000/backup/ \
     -H "Content-Type: application/json" \
     -d '{"prefix": "emergency"}'
   ```

**Prevention**: 
- Monitor backup backend health regularly
- Set up alerts for backup backend failures
- Use the health endpoints for proactive monitoring

## File Structure

```
backend/
├── src/
│   ├── data-source.ts          # TypeORM configuration for migrations
│   └── **/*.entity.ts         # Entity definitions
├── migration/                  # Migration files
│   └── *.ts                   # Generated migration files
├── package.json               # NPM scripts
└── .env                      # Environment variables for backup backend
```

## Migration Examples

### Adding a New Column
```typescript
// 1. Modify entity
@Entity()
export class User {
  @Column({ nullable: true })
  phoneNumber: string;
}

// 2. Generate migration
npm run migration:generate -- -n AddPhoneNumberToUsers

// 3. Run migration (creates backup via API automatically)
npm run migration:run
```

### Creating a New Table
```typescript
// 1. Create new entity
@Entity()
export class Notification {
  @PrimaryColumn("uuid")
  id: string;
  
  @Column()
  message: string;
}

// 2. Generate migration
npm run migration:generate -- -n CreateNotificationsTable

// 3. Run migration (creates backup via API automatically)
npm run migration:run
```

### Modifying Relationships
```typescript
// 1. Update entity relationships
@Entity()
export class Order {
  @ManyToOne(() => Customer)
  @JoinColumn({ name: "customer_id" })
  customer: Customer;
}

// 2. Generate migration
npm run migration:generate -- -n UpdateOrderCustomerRelationship

// 3. Run migration (creates backup via API automatically)
npm run migration:run
```

## Security Considerations

1. **Backup Security**: Backup files contain sensitive data and are managed centrally
2. **Environment Variables**: Keep `.env` file secure
3. **Database Credentials**: Use strong passwords
4. **Access Control**: Limit who can run migrations
5. **Audit Trail**: Keep migration history for compliance
6. **API Security**: Ensure backup backend API is properly secured

## Performance Tips

1. **Batch Operations**: Use batch inserts/updates for large datasets
2. **Indexes**: Add indexes after data migration
3. **Downtime Planning**: Plan maintenance windows for large migrations
4. **Testing**: Test migration performance with production-like data
5. **Monitoring**: Monitor database performance during migrations
6. **Backup Monitoring**: Use the monitoring endpoints to track backup performance

## API Integration Examples

### Health Monitoring Script
```bash
#!/bin/bash
# Check backup backend health before running migrations

HEALTH_RESPONSE=$(curl -s -X GET http://localhost:5000/health/)
STATUS=$(echo $HEALTH_RESPONSE | jq -r '.status')

if [ "$STATUS" != "healthy" ]; then
    echo "Backup backend is not healthy: $STATUS"
    exit 1
fi

echo "Backup backend is healthy, proceeding with migration"
npm run migration:run
```

### Automated Backup Before Migration
```bash
#!/bin/bash
# Create backup before running migration

echo "Creating backup before migration..."
BACKUP_RESPONSE=$(curl -s -X POST http://localhost:5000/backup/ \
  -H "Content-Type: application/json" \
  -d '{"prefix": "pre_migration"}')

BACKUP_STATUS=$(echo $BACKUP_RESPONSE | jq -r '.status')

if [ "$BACKUP_STATUS" != "success" ]; then
    echo "Backup creation failed: $BACKUP_RESPONSE"
    exit 1
fi

echo "Backup created successfully, running migration..."
npm run migration:run
```

### Migration Rollback with API
```bash
#!/bin/bash
# Rollback migration using API

echo "Rolling back migration..."
REVERT_RESPONSE=$(npm run migration:revert)

if [ $? -ne 0 ]; then
    echo "Migration revert failed, restoring from backup..."
    
    # Get latest backup and restore
    LATEST_BACKUP=$(curl -s -X GET http://localhost:5000/backup/list | jq -r '.data[0].name')
    
    RESTORE_RESPONSE=$(curl -s -X POST http://localhost:5000/backup/restore \
      -H "Content-Type: application/json" \
      -d "{\"backup_name\": \"$LATEST_BACKUP\"}")
    
    RESTORE_STATUS=$(echo $RESTORE_RESPONSE | jq -r '.status')
    
    if [ "$RESTORE_STATUS" != "success" ]; then
        echo "Backup restoration failed: $RESTORE_RESPONSE"
        exit 1
    fi
    
    echo "Successfully restored from backup: $LATEST_BACKUP"
else
    echo "Migration reverted successfully"
fi
``` 