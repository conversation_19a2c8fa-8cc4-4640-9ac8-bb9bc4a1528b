# TypeORM Migration Workflow

> **⚠️ DEPRECATED**: This document is deprecated. Please use the new [Migration Workflow with Automated Backup](./MIGRATION_WORKFLOW.md) instead.

## Setup (Add to package.json scripts)
```json
"typeorm": "typeorm-ts-node-commonjs",
"migration:generate": "npm run typeorm migration:generate -- -d src/data-source.ts",
"migration:run": "npm run typeorm migration:run -- -d src/data-source.ts",
"migration:revert": "npm run typeorm migration:revert -- -d src/data-source.ts",
"migration:status": "python scripts/migration_manager_ysqlsh.py status"
```

## Generate Migration
```bash
npm run migration:generate -- -n MigrationName
```

## Run Migrations
```bash
npm run migration:run
```

## Revert Migration
```bash
npm run migration:revert
```

## Show Migration Status
```bash
npm run migration:status
```



---

## ⚠️ Important Notice

**This basic workflow does NOT include automatic backups or rollback capabilities.**

For production use, please use the new automated migration system:

- **Safe migrations with backup**: `npm run migration:run-safe`
- **Safe rollback with backup**: `npm run migration:revert-safe`
- **Complete documentation**: [Migration Workflow with Automated Backup](./MIGRATION_WORKFLOW.md) 