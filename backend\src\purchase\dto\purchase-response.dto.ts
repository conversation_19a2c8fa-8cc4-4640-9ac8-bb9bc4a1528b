import { ApiProperty } from "@nestjs/swagger";
import { PaymentMethods, PurchaseStatus } from "../purchase.entity";

export class PurchaseItemResponseDto {
  @ApiProperty({ example: "uuid-v7-string" })
  uuid: string;

  @ApiProperty({ example: "uuid-v7-string" })
  purchaseUuid: string;

  @ApiProperty({ example: "uuid-v7-string" })
  productUuid: string;

  @ApiProperty({ example: "Product Name" })
  name: string;

  @ApiProperty({ example: 5 })
  quantity: number;

  @ApiProperty({ example: 25.99 })
  unitPrice: number;

  @ApiProperty({ example: 129.95 })
  lineTotal: number;

  @ApiProperty({ example: 5.25 })
  taxAmount: number;

  @ApiProperty({ example: 0 })
  order: number;

  @ApiProperty({ example: "2025-01-15T10:30:00.000Z" })
  createdAt: string;

  @ApiProperty({ example: "2025-01-15T10:30:00.000Z" })
  updatedAt: string;
}

export class PurchaseResponseDto {
  @ApiProperty({ example: "uuid-v7-string" })
  uuid: string;

  @ApiProperty({ example: "PO-20250115-001" })
  purchaseNumber: string;

  @ApiProperty({ example: "uuid-v7-string", required: false })
  supplierUuid?: string;

  @ApiProperty({ example: "Supplier Name", required: false })
  supplierName?: string;

  @ApiProperty({ example: "12345678901", required: false })
  supplierFiscalId?: string;

  @ApiProperty({ example: "RC123456", required: false })
  supplierRc?: string;

  @ApiProperty({ example: "ART001", required: false })
  supplierArticleNumber?: string;

  @ApiProperty({ example: "uuid-v7-string", required: false })
  warehouseUuid?: string;

  @ApiProperty({ example: "Warehouse Name", required: false })
  warehouseName?: string;

  @ApiProperty({ example: 150.0 })
  subtotal: number;

  @ApiProperty({ example: true })
  useTax: boolean;

  @ApiProperty({ example: 0.1 })
  taxRate: number;

  @ApiProperty({ example: 15.0 })
  taxAmount: number;

  @ApiProperty({ example: 165.0 })
  totalAmount: number;

  @ApiProperty({ example: 100.0 })
  amountPaid: number;

  @ApiProperty({ example: 65.0 })
  balanceDue: number;

  @ApiProperty({ example: PaymentMethods.BANK_TRANSFER, enum: PaymentMethods })
  paymentMethod: PaymentMethods;

  @ApiProperty({ example: ["2025-01-15T10:30:00.000Z"], required: false })
  paymentDate?: string[];

  @ApiProperty({ example: "2025-01-15T10:30:00.000Z" })
  purchaseDate: string;

  @ApiProperty({ example: "2025-02-14T10:30:00.000Z" })
  dueDate: string;

  @ApiProperty({ example: PurchaseStatus.UNPAID, enum: PurchaseStatus })
  status: PurchaseStatus;

  @ApiProperty({ example: "uuid-v7-string" })
  createdBy: string;

  @ApiProperty({ example: "uuid-v7-string" })
  updatedBy: string;

  @ApiProperty({ example: "Purchase for Q1 inventory restocking", required: false })
  notes?: string;

  @ApiProperty({ example: false })
  isDeleted: boolean;

  @ApiProperty({ example: "2025-01-15T10:30:00.000Z" })
  createdAt: string;

  @ApiProperty({ example: "2025-01-15T10:30:00.000Z" })
  updatedAt: string;

  @ApiProperty({ type: [PurchaseItemResponseDto] })
  purchaseItems: PurchaseItemResponseDto[];
}

export function toPurchaseItemResponseDto(entity: any): PurchaseItemResponseDto {
  return {
    uuid: entity.id,
    purchaseUuid: entity.purchaseUuid,
    productUuid: entity.productUuid,
    name: entity.name,
    quantity: entity.quantity,
    unitPrice: entity.unitPrice,
    lineTotal: entity.lineTotal,
    taxAmount: entity.taxAmount,
    order: entity.order,
    createdAt: entity.createdAt?.toISOString(),
    updatedAt: entity.updatedAt?.toISOString(),
  };
}

export function toPurchaseResponseDto(entity: any): PurchaseResponseDto {
  return {
    uuid: entity.id,
    purchaseNumber: entity.purchaseNumber,
    supplierUuid: entity.supplierUuid,
    supplierName: entity.supplierName,
    supplierFiscalId: entity.supplierFiscalId,
    supplierRc: entity.supplierRc,
    supplierArticleNumber: entity.supplierArticleNumber,
    warehouseUuid: entity.warehouseUuid,
    warehouseName: entity.warehouseName,
    subtotal: entity.subtotal,
    useTax: entity.useTax,
    taxRate: entity.taxRate,
    taxAmount: entity.taxAmount,
    totalAmount: entity.totalAmount,
    amountPaid: entity.amountPaid,
    balanceDue: entity.balanceDue,
    paymentMethod: entity.paymentMethod,
    paymentDate: entity.paymentDate,
    purchaseDate: entity.purchaseDate?.toISOString(),
    dueDate: entity.dueDate?.toISOString(),
    status: entity.status,
    createdBy: entity.createdBy,
    updatedBy: entity.updatedBy,
    notes: entity.notes,
    isDeleted: entity.isDeleted,
    createdAt: entity.createdAt?.toISOString(),
    updatedAt: entity.updatedAt?.toISOString(),
    purchaseItems: entity.purchaseItems?.map(toPurchaseItemResponseDto) || [],
  };
}

export function toPurchaseResponseDtoArray(entities: any[]): PurchaseResponseDto[] {
  return entities.map(toPurchaseResponseDto);
} 