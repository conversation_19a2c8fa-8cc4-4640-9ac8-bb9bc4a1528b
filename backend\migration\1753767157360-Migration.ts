import { MigrationInterface, QueryRunner } from "typeorm";

export class Migration1753767157360 implements MigrationInterface {
    name = 'Migration1753767157360'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_c465641423669ca997574fc976"`);
        await queryRunner.query(`CREATE TABLE "purchase_return_items" ("id" uuid NOT NULL, "purchaseReturnUuid" uuid NOT NULL, "purchaseItemUuid" uuid NOT NULL, "productUuid" uuid NOT NULL, "storageUuid" uuid, "name" character varying NOT NULL, "quantity" numeric(10,2) NOT NULL, "unitPrice" numeric(10,2) NOT NULL, "lineTotal" numeric(10,2) NOT NULL, "itemReturnReason" character varying, "createdBy" uuid NOT NULL, "updatedBy" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_b392d1493da8d20117cc5f0c135" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_9281c984fcef0610adc127ce65" ON "purchase_return_items" ("purchaseReturnUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_460bddf841537378a5b1c47a8f" ON "purchase_return_items" ("purchaseItemUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_8a31fce667a732befbee4f52d8" ON "purchase_return_items" ("productUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_80a0ebc6f2fdba61e508e7768f" ON "purchase_return_items" ("storageUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_dafcc45a2e3138fe97befcd948" ON "purchase_return_items" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_63984daddac7f350e354422f61" ON "purchase_return_items" ("updatedBy") `);
        await queryRunner.query(`CREATE TYPE "public"."purchase_returns_status_enum" AS ENUM('pending', 'approved', 'shipped', 'received_by_supplier', 'refunded', 'cancelled')`);
        await queryRunner.query(`CREATE TABLE "purchase_returns" ("id" uuid NOT NULL, "returnNumber" character varying NOT NULL, "purchaseUuid" uuid NOT NULL, "supplierUuid" uuid, "supplierName" character varying, "warehouseUuid" uuid, "warehouseName" character varying, "returnReason" character varying NOT NULL, "returnDate" TIMESTAMP NOT NULL, "expectedReturnDate" TIMESTAMP, "returnDescription" character varying, "totalReturnAmount" numeric(10,2) NOT NULL DEFAULT '0', "status" "public"."purchase_returns_status_enum" NOT NULL DEFAULT 'pending', "createdBy" uuid NOT NULL, "updatedBy" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "approvedAt" TIMESTAMP, "approvedBy" uuid, "shippedAt" TIMESTAMP, "shippedBy" uuid, "receivedBySupplierAt" TIMESTAMP, "refundedAt" TIMESTAMP, "refundedBy" uuid, "trackingNumber" character varying, "shippingMethod" character varying, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_448bb11c381cb6275453a448f32" UNIQUE ("returnNumber"), CONSTRAINT "PK_cc2ea54a32938fc38a4e5442330" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_cf3c88d98624ea91f0450ee002" ON "purchase_returns" ("purchaseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_b28895b55bd83122fd27bfa9a1" ON "purchase_returns" ("supplierUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_ae6350a6153b8ad0a619bc6282" ON "purchase_returns" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_508f79dce5f4a0bae6736e0424" ON "purchase_returns" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_788001dca4a6c3ea048b88a2c2" ON "purchase_returns" ("updatedBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_61a51995e5e8330b12c7789e54" ON "purchase_returns" ("approvedBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_75a8eddce7a616e36ebdf7bffe" ON "purchase_returns" ("shippedBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_5ee514c4caecbeb106a2d2e0fe" ON "purchase_returns" ("refundedBy") `);
        await queryRunner.query(`CREATE TABLE "purchase_items" ("id" uuid NOT NULL, "purchaseUuid" uuid NOT NULL, "productUuid" uuid NOT NULL, "name" character varying NOT NULL, "quantity" numeric(10,2) NOT NULL, "unitPrice" numeric(10,2) NOT NULL, "lineTotal" numeric(10,2) NOT NULL, "taxAmount" numeric(10,2) NOT NULL DEFAULT '0', "order" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_e3d9bea880baad86ff6de3290da" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_244d340079336fc2662e8ac26e" ON "purchase_items" ("purchaseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_a3efdc78e0731c8906e8ed59f5" ON "purchase_items" ("productUuid") `);
        await queryRunner.query(`CREATE TABLE "goods_receipt_items" ("id" uuid NOT NULL, "goodsReceiptUuid" uuid NOT NULL, "purchaseItemUuid" uuid NOT NULL, "productUuid" uuid NOT NULL, "name" character varying NOT NULL, "quantityOrdered" numeric(10,2) NOT NULL, "quantityReceived" numeric(10,2) NOT NULL, "quantityRejected" numeric(10,2) NOT NULL DEFAULT '0', "unitPrice" numeric(10,2) NOT NULL, "notes" character varying, "order" integer NOT NULL DEFAULT '0', "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_3773489ac01faa49777eed0a14f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_48a6ff0d7bc09b3dbeb6a92c46" ON "goods_receipt_items" ("goodsReceiptUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_33ed27e3c56d676618427bb266" ON "goods_receipt_items" ("purchaseItemUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_ab44b562ecb6b739e3cdb06a07" ON "goods_receipt_items" ("productUuid") `);
        await queryRunner.query(`CREATE TYPE "public"."goods_receipts_status_enum" AS ENUM('pending', 'partial', 'completed', 'cancelled')`);
        await queryRunner.query(`CREATE TABLE "goods_receipts" ("id" uuid NOT NULL, "receiptNumber" character varying NOT NULL, "purchaseUuid" uuid NOT NULL, "supplierUuid" uuid NOT NULL, "supplierName" character varying NOT NULL, "warehouseUuid" uuid NOT NULL, "warehouseName" character varying NOT NULL, "receiptDate" TIMESTAMP NOT NULL, "expectedReceiptDate" TIMESTAMP NOT NULL, "status" "public"."goods_receipts_status_enum" NOT NULL DEFAULT 'pending', "notes" character varying, "createdBy" uuid NOT NULL, "updatedBy" uuid NOT NULL, "isDeleted" boolean NOT NULL DEFAULT false, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "UQ_f7bb0b4b6ab0f30203f234bbf48" UNIQUE ("receiptNumber"), CONSTRAINT "PK_f8cac411be0211f923e1be8534f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d1545277a5379c75ba409feca4" ON "goods_receipts" ("purchaseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_377f422c705ba5b56d777abede" ON "goods_receipts" ("supplierUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_962d19a5b91ee1ce2d9ee6eb66" ON "goods_receipts" ("warehouseUuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_84a51a438a0dae0543000d6774" ON "goods_receipts" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_32204f83b8586dda3bab2be486" ON "goods_receipts" ("updatedBy") `);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "userUuid"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "itemsSnapshot"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "invoiceDate"`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "purchaseNumber" character varying NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD CONSTRAINT "UQ_59712045f2664aeb8a046928981" UNIQUE ("purchaseNumber")`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "supplierName" character varying`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "supplierFiscalId" character varying`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "supplierRc" character varying`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "supplierArticleNumber" character varying`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "warehouseName" character varying`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "subtotal" numeric(10,2) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "useTax" boolean NOT NULL DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "taxRate" numeric(5,4) NOT NULL DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "taxAmount" numeric(10,2) NOT NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "purchaseDate" TIMESTAMP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "expectedDeliveryDate" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "actualDeliveryDate" TIMESTAMP`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "orderUuid" uuid`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "notes" character varying`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "createdBy" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "updatedBy" uuid NOT NULL`);
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "warehouseUuid" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "totalAmount" DROP DEFAULT`);
        await queryRunner.query(`ALTER TYPE "public"."purchases_paymentmethod_enum" RENAME TO "purchases_paymentmethod_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."purchases_paymentmethod_enum" AS ENUM('cash', 'credit_card', 'bank_transfer', 'mobile_payment', 'cheque', 'credit_terms', 'other')`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "paymentMethod" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "paymentMethod" TYPE "public"."purchases_paymentmethod_enum" USING "paymentMethod"::"text"::"public"."purchases_paymentmethod_enum"`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "paymentMethod" SET DEFAULT 'bank_transfer'`);
        await queryRunner.query(`DROP TYPE "public"."purchases_paymentmethod_enum_old"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "paymentDate"`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "paymentDate" jsonb`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "dueDate" DROP DEFAULT`);
        await queryRunner.query(`ALTER TYPE "public"."purchases_status_enum" RENAME TO "purchases_status_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."purchases_status_enum" AS ENUM('draft', 'pending_approval', 'approved', 'ordered', 'partially_received', 'received', 'fully_received', 'unpaid', 'partially_paid', 'paid', 'cancelled', 'returned')`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "status" TYPE "public"."purchases_status_enum" USING "status"::"text"::"public"."purchases_status_enum"`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "status" SET DEFAULT 'unpaid'`);
        await queryRunner.query(`DROP TYPE "public"."purchases_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT '0.1'`);
        await queryRunner.query(`CREATE INDEX "IDX_8f7706dbabfb64c969f44cee56" ON "purchases" ("createdBy") `);
        await queryRunner.query(`CREATE INDEX "IDX_9335e1c154f8f6490a199f1105" ON "purchases" ("updatedBy") `);
        await queryRunner.query(`ALTER TABLE "purchase_return_items" ADD CONSTRAINT "FK_9281c984fcef0610adc127ce657" FOREIGN KEY ("purchaseReturnUuid") REFERENCES "purchase_returns"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "purchase_items" ADD CONSTRAINT "FK_244d340079336fc2662e8ac26eb" FOREIGN KEY ("purchaseUuid") REFERENCES "purchases"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "goods_receipt_items" ADD CONSTRAINT "FK_48a6ff0d7bc09b3dbeb6a92c46e" FOREIGN KEY ("goodsReceiptUuid") REFERENCES "goods_receipts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "goods_receipt_items" DROP CONSTRAINT "FK_48a6ff0d7bc09b3dbeb6a92c46e"`);
        await queryRunner.query(`ALTER TABLE "purchase_items" DROP CONSTRAINT "FK_244d340079336fc2662e8ac26eb"`);
        await queryRunner.query(`ALTER TABLE "purchase_return_items" DROP CONSTRAINT "FK_9281c984fcef0610adc127ce657"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9335e1c154f8f6490a199f1105"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8f7706dbabfb64c969f44cee56"`);
        await queryRunner.query(`ALTER TABLE "orders" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`CREATE TYPE "public"."purchases_status_enum_old" AS ENUM('paid', 'partially_paid', 'unpaid', 'cancelled')`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "status" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "status" TYPE "public"."purchases_status_enum_old" USING "status"::"text"::"public"."purchases_status_enum_old"`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "status" SET DEFAULT 'unpaid'`);
        await queryRunner.query(`DROP TYPE "public"."purchases_status_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."purchases_status_enum_old" RENAME TO "purchases_status_enum"`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "dueDate" SET DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "paymentDate"`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "paymentDate" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`CREATE TYPE "public"."purchases_paymentmethod_enum_old" AS ENUM('cash', 'credit_card', 'bank_transfer', 'mobile_payment', 'cheque', 'other')`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "paymentMethod" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "paymentMethod" TYPE "public"."purchases_paymentmethod_enum_old" USING "paymentMethod"::"text"::"public"."purchases_paymentmethod_enum_old"`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "paymentMethod" SET DEFAULT 'cash'`);
        await queryRunner.query(`DROP TYPE "public"."purchases_paymentmethod_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."purchases_paymentmethod_enum_old" RENAME TO "purchases_paymentmethod_enum"`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "totalAmount" SET DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE "purchases" ALTER COLUMN "warehouseUuid" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "sales" ALTER COLUMN "taxRate" SET DEFAULT 0.1`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "updatedBy"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "createdBy"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "notes"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "orderUuid"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "actualDeliveryDate"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "expectedDeliveryDate"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "purchaseDate"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "taxAmount"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "taxRate"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "useTax"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "subtotal"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "warehouseName"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "supplierArticleNumber"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "supplierRc"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "supplierFiscalId"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "supplierName"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP CONSTRAINT "UQ_59712045f2664aeb8a046928981"`);
        await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "purchaseNumber"`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "invoiceDate" TIMESTAMP NOT NULL DEFAULT now()`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "itemsSnapshot" jsonb NOT NULL DEFAULT '[]'`);
        await queryRunner.query(`ALTER TABLE "purchases" ADD "userUuid" uuid NOT NULL`);
        await queryRunner.query(`DROP INDEX "public"."IDX_32204f83b8586dda3bab2be486"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_84a51a438a0dae0543000d6774"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_962d19a5b91ee1ce2d9ee6eb66"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_377f422c705ba5b56d777abede"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_d1545277a5379c75ba409feca4"`);
        await queryRunner.query(`DROP TABLE "goods_receipts"`);
        await queryRunner.query(`DROP TYPE "public"."goods_receipts_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ab44b562ecb6b739e3cdb06a07"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_33ed27e3c56d676618427bb266"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_48a6ff0d7bc09b3dbeb6a92c46"`);
        await queryRunner.query(`DROP TABLE "goods_receipt_items"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a3efdc78e0731c8906e8ed59f5"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_244d340079336fc2662e8ac26e"`);
        await queryRunner.query(`DROP TABLE "purchase_items"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5ee514c4caecbeb106a2d2e0fe"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_75a8eddce7a616e36ebdf7bffe"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_61a51995e5e8330b12c7789e54"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_788001dca4a6c3ea048b88a2c2"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_508f79dce5f4a0bae6736e0424"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ae6350a6153b8ad0a619bc6282"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b28895b55bd83122fd27bfa9a1"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_cf3c88d98624ea91f0450ee002"`);
        await queryRunner.query(`DROP TABLE "purchase_returns"`);
        await queryRunner.query(`DROP TYPE "public"."purchase_returns_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_63984daddac7f350e354422f61"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_dafcc45a2e3138fe97befcd948"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_80a0ebc6f2fdba61e508e7768f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_8a31fce667a732befbee4f52d8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_460bddf841537378a5b1c47a8f"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_9281c984fcef0610adc127ce65"`);
        await queryRunner.query(`DROP TABLE "purchase_return_items"`);
        await queryRunner.query(`CREATE INDEX "IDX_c465641423669ca997574fc976" ON "purchases" ("userUuid") `);
    }

}
