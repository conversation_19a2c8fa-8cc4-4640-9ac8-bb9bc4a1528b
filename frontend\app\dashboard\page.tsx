'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useDashboardData } from './hooks/useDashboardData';
import { DashboardCard } from './components/DashboardCard';
import { QuickActionButton } from './components/QuickActionButton';
import { RecentActivity } from './components/RecentActivity';

export default function Dashboard() {
  const { user, token, loading: authLoading } = useAuth();
  const { 
    dashboardStats, 
    isLoading, 
    error, 
    userRole, 
    warehouseName, 
    dashboardCards, 
    quickActions, 
    refetch 
  } = useDashboardData();

  console.log('[Dashboard] Component render state:', {
    hasUser: !!user,
    hasToken: !!token,
    authLoading,
    dashboardLoading: isLoading,
    hasError: !!error
  });

  // Show loading state while auth is being checked
  if (authLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-lg text-gray-600">Loading authentication...</span>
          </div>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user || !token) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-yellow-800 font-semibold">Authentication Required</h3>
          <p className="text-yellow-600 mt-1">
            Please log in to access the dashboard.
          </p>
          <a
            href="/auth"
            className="mt-3 inline-block px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
          >
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  // Get user display name
  const userDisplayName = user?.displayName || user?.email || 'User';

  // Handle refresh
  const handleRefresh = () => {
    refetch();
  };

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Error Loading Dashboard</h3>
          <p className="text-red-600 mt-1">
            {error instanceof Error ? error.message : 'An unexpected error occurred'}
          </p>
          <button
            onClick={handleRefresh}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Welcome back, {userDisplayName}! Here's an overview of your {warehouseName}.
          </p>
          <p className="text-sm text-gray-500 mt-1">
            Role: {userRole.charAt(0).toUpperCase() + userRole.slice(1).replace('_', ' ')}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          {isLoading && (
            <div className="flex items-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm">Updating...</span>
            </div>
          )}
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors flex items-center space-x-2"
            disabled={isLoading}
          >
            <span>🔄</span>
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <QuickActionButton
              key={index}
              title={action.title}
              icon={action.icon}
              color={action.color as any}
              link={action.link}
            />
          ))}
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {dashboardCards.map((card, index) => (
          <DashboardCard
            key={index}
            title={card.title}
            value={card.value}
            icon={card.icon}
            color={card.color as any}
            link={card.link}
          />
        ))}
      </div>

      {/* Recent Activity and Additional Info */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity */}
        <div className="lg:col-span-2">
          <RecentActivity 
            events={dashboardStats.securityEvents} 
            isLoading={isLoading}
          />
        </div>

        {/* System Status */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">System Status</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium text-green-800">System Online</span>
              </div>
              <span className="text-xs text-green-600">All systems operational</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-blue-800">Database</span>
              </div>
              <span className="text-xs text-blue-600">Connected</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <span className="text-sm font-medium text-purple-800">API Services</span>
              </div>
              <span className="text-xs text-purple-600">Running</span>
            </div>
          </div>

          {/* Last Updated */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 text-center">
              Last updated: {new Date().toLocaleTimeString()}
            </p>
          </div>
        </div>
      </div>

      {/* Welcome Message for New Users */}
      {dashboardStats.products === 0 && dashboardStats.warehouses === 0 && !isLoading && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <div className="text-2xl">🎉</div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900">Welcome to Dido Distribution!</h3>
              <p className="text-blue-700 mt-1">
                Get started by adding your first products and warehouses. Use the quick actions above to begin.
              </p>
              <div className="mt-3 flex space-x-3">
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                  Add First Product
                </button>
                <button className="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                  View Tutorial
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
