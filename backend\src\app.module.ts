import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UsersModule } from "./users/users.module";
import { AuthModule } from "./auth/auth.module";
import { WarehousesModule } from "./warehouses/warehouses.module";
import { ProductsModule } from "./products/products.module";
import { SalesModule } from "./sales/sales.module";
import { OrdersModule } from "./orders/orders.module";
import { QuotesModule } from "./quotes/quotes.module";
import { PurchaseModule } from "./purchase/purchase.module";
import { CustomersModule } from "./customers/customers.module";
import { InventoryModule } from "./inventory/inventory.module";
import { StockComputationModule } from "./stock-computation/stock-computation.module";
import { CommonModule } from "./common/common.module";
import { SuppliersModule } from "./suppliers/suppliers.module";
import { VansModule } from "./vans/vans.module";
import { RegionsModule } from "./regions/regions.module";
import { RoutesModule } from "./routes/routes.module";
import { CompaniesModule } from "./companies/companies.module";
import { AccountSettingsModule } from "./account-settings/account-settings.module";
import { UserAccountPlansModule } from "./user-account-plans/user-account-plans.module";
import { ProductCategoriesModule } from "./product-categories/product-categories.module";
import { DatabaseTestModule } from "./database-test/database-test.module";
import { LogsModule } from "./logs/logs.module";

// Import entities
import { User } from "./users/user.entity";
import { Role } from "./users/role.entity";
import { AuthAudit } from "./auth/auth-audit.entity";
import { RateLimit } from "./auth/rate-limit.entity";
import { RefreshToken } from "./auth/refresh-token.entity";
import { Product } from "./products/product.entity";
import { Region } from "./regions/region.entity";
import { Customer } from "./customers/customer.entity";
import { CustomerPayment } from "./customers/customer-payment.entity";
import { CreditAdjustment } from "./customers/credit-adjustment.entity";
import { Van } from "./vans/van.entity";
import { Storage } from "./inventory/storage.entity";
import { StockAdjustment } from "./inventory/stock-adjustment.entity";
import { InventoryItem } from "./inventory/inventory-item.entity";
import { Warehouse } from "./warehouses/warehouse.entity";
import { Company } from "./companies/company.entity";
import { Supplier } from "./suppliers/supplier.entity";
import { Purchase } from "./purchase/purchase.entity";
import { PurchaseItem } from "./purchase/purchase-item.entity";
import { PurchaseReturn } from "./purchase/purchase-return.entity";
import { PurchaseReturnItem } from "./purchase/purchase-return-item.entity";
import { GoodsReceipt } from "./purchase/goods-receipt.entity";
import { GoodsReceiptItem } from "./purchase/goods-receipt-item.entity";
import { ProductCategory } from "./product-categories/product-category.entity";
import { AccountPlan } from "./user-account-plans/account-plan.entity";
import { Feature } from "./user-account-plans/feature.entity";
import { Route } from "./routes/route.entity";
import { AccountSettings } from "./account-settings/account-settings.entity";
import { Sale } from "./sales/sale.entity";
import { SaleItem } from "./sales/sale-item.entity";
import { Order } from "./orders/order.entity";
import { OrderItem } from "./orders/order-item.entity";
import { Quote } from "./quotes/quote.entity";
import { QuoteItem } from "./quotes/quote-item.entity";
import { Log } from "./logs/log.entity";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: false, // Disables caching of environment variables
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const logger = new Logger("TypeOrmModule");
        const host = configService.get<string>("YUGABYTE_HOST");
        const port = configService.get<number>("YUGABYTE_PORT");
        const database = configService.get<string>("YUGABYTE_DATABASE");
        const user = configService.get<string>("YUGABYTE_USER");
        const password = configService.get<string>("YUGABYTE_PASSWORD");
        
        logger.log(`Connecting to YugabyteDB: ${host}:${port}/${database}`);
        
        return {
          type: 'postgres',
          host,
          port,
          database,
          username: user,
          password,
          entities: [User, Role, AuthAudit, RateLimit, RefreshToken, Product, Region, Customer, CustomerPayment, CreditAdjustment, Van, Storage, StockAdjustment, InventoryItem, Warehouse, Company, Supplier, Purchase, PurchaseItem, PurchaseReturn, PurchaseReturnItem, GoodsReceipt, GoodsReceiptItem, ProductCategory, AccountPlan, Feature, Route, AccountSettings, Sale, SaleItem, Order, OrderItem, Quote, QuoteItem, Log],
          synchronize: false, // Re-enabled for fresh database
          logging: ['error', 'warn'],
          ssl: false, // Set to true if using SSL
        };
      },
      inject: [ConfigService],
    }),
    UsersModule,
    AuthModule,
    WarehousesModule,
    SuppliersModule,
    ProductsModule,
    SalesModule,
    OrdersModule,
    QuotesModule,
    PurchaseModule,
    CustomersModule,
    InventoryModule,
    StockComputationModule,
    CommonModule,
    VansModule,
    RegionsModule,
    RoutesModule,
    CompaniesModule,
    AccountSettingsModule,
    UserAccountPlansModule,
    ProductCategoriesModule,
    DatabaseTestModule,
    LogsModule,
  ],
})
export class AppModule {}
