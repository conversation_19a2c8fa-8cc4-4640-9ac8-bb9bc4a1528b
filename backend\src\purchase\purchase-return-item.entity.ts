import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, ManyToOne, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { PurchaseReturn } from './purchase-return.entity';

@Entity('purchase_return_items')
export class PurchaseReturnItem {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase return item (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase return",
  })
  @Column('uuid')
  @Index()
  purchaseReturnUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase item being returned",
  })
  @Column('uuid')
  @Index()
  purchaseItemUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @Column('uuid')
  @Index()
  productUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the storage where the item is located",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  storageUuid?: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of return",
  })
  @Column()
  name: string;

  @ApiProperty({
    example: 2,
    description: "Quantity being returned",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  quantity: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of purchase (for refund calculation)",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  unitPrice: number;

  @ApiProperty({
    example: 51.98,
    description: "Line total for this return item",
  })
  @Column('decimal', { precision: 10, scale: 2 })
  lineTotal: number;

  @ApiProperty({
    example: "Damaged during shipping",
    description: "Reason for returning this specific item",
    required: false,
  })
  @Column({ nullable: true })
  itemReturnReason?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the return item",
  })
  @Column('uuid')
  @Index()
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the return item",
  })
  @Column('uuid')
  @Index()
  updatedBy: string;

  @ApiProperty({
    example: false,
    description: "Whether the return item has been soft deleted",
  })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to PurchaseReturn
  @ManyToOne(() => PurchaseReturn, purchaseReturn => purchaseReturn.returnItems)
  @JoinColumn({ name: 'purchaseReturnUuid' })
  purchaseReturn: PurchaseReturn;

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 