import { getAuthHeadersWithContentType } from '@/utils/authHeaders';

const API_BASE = '/api/logs';

export interface Log {
  id: string;
  userUuid: string;
  operation: string;
  entity: string;
  description?: string;
  data?: Record<string, any>; // JSON data field
  createdAt: Date;
}

export interface LogFilter {
  userUuid?: string;
  operation?: string;
  entity?: string;
  description?: string;
  page?: number;
  limit?: number;
}

export interface CreateLogData {
  userUuid: string;
  operation: string;
  entity: string;
  description?: string;
  data?: Record<string, any>;
}

export interface PaginatedLogsResponse {
  data: Log[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface AvailableOperationsResponse {
  operations: string[];
}

export interface AvailableEntitiesResponse {
  entities: string[];
}

/**
 * Get all logs with optional filtering and pagination
 */
export async function getLogs(filters: LogFilter = {}): Promise<PaginatedLogsResponse> {
  const queryParams = new URLSearchParams();
  
  if (filters.userUuid) queryParams.append('userUuid', filters.userUuid);
  if (filters.operation) queryParams.append('operation', filters.operation);
  if (filters.entity) queryParams.append('entity', filters.entity);
  if (filters.description) queryParams.append('description', filters.description);
  if (filters.page) queryParams.append('page', filters.page.toString());
  if (filters.limit) queryParams.append('limit', filters.limit.toString());

  const url = `${API_BASE}?${queryParams.toString()}`;
  
  const response = await fetch(url, {
    method: 'GET',
    headers: getAuthHeadersWithContentType(),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch logs: ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Get a specific log by ID
 */
export async function getLogById(id: string): Promise<Log> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'GET',
    headers: getAuthHeadersWithContentType(),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch log: ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Get all available operations
 */
export async function getAvailableOperations(): Promise<AvailableOperationsResponse> {
  const response = await fetch(`${API_BASE}/operations/available`, {
    method: 'GET',
    headers: getAuthHeadersWithContentType(),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch available operations: ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Get all available entities
 */
export async function getAvailableEntities(): Promise<AvailableEntitiesResponse> {
  const response = await fetch(`${API_BASE}/entities/available`, {
    method: 'GET',
    headers: getAuthHeadersWithContentType(),
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch available entities: ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Create a new log entry
 */
export async function createLog(data: CreateLogData): Promise<Log> {
  const response = await fetch(API_BASE, {
    method: 'POST',
    headers: getAuthHeadersWithContentType(),
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error(`Failed to create log: ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Delete a log entry by ID
 */
export async function deleteLog(id: string): Promise<void> {
  const response = await fetch(`${API_BASE}/${id}`, {
    method: 'DELETE',
    headers: getAuthHeadersWithContentType(),
  });

  if (!response.ok) {
    throw new Error(`Failed to delete log: ${response.statusText}`);
  }
}

/**
 * DANGEROUS: Delete all logs from the database
 * 
 * ⚠️ WARNING: This operation is IRREVERSIBLE and will permanently delete ALL logs
 * This function should ONLY be used in development/testing environments
 * 
 * @returns Object containing deletion statistics
 */
export async function deleteAllLogs(): Promise<{ deletedCount: number; message: string }> {
  // Show a console warning in development
  if (process.env.NODE_ENV === 'development') {
    console.warn(`
      ⚠️ DANGEROUS OPERATION - NOT FOR PRODUCTION USE ⚠️
      
      You are about to delete ALL logs from the database.
      This action is IRREVERSIBLE and will result in complete loss of audit trail.
      
      If you're sure you want to proceed, this function will:
      - Permanently delete all logs from the database
      - Remove all audit history
      - This action cannot be undone
      
      Consider alternatives for production:
      - Implement log rotation/archiving
      - Use soft deletes instead
      - Set up log retention policies
    `);
  }

  const response = await fetch(`${API_BASE}/all`, {
    method: 'DELETE',
    headers: getAuthHeadersWithContentType(),
  });

  if (!response.ok) {
    throw new Error(`Failed to delete all logs: ${response.statusText}`);
  }

  return await response.json();
}

// Export the API object for use in components
export const logsApi = {
  getLogs,
  getLogById,
  getAvailableOperations,
  getAvailableEntities,
  createLog,
  deleteLog,
  deleteAllLogs, // Added with warnings
}; 