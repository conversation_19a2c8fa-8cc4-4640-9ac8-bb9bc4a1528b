import { useState } from 'react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { getSale, cancelSale, Sale } from '../salesApi';
import { getCompaniesByUser } from '../../../settings/companiesApi';
import { getCustomerByUuid } from '../../customers/customersApi';
import { updateUrlParams } from '../salesHelpers';
import { filterCustomers } from '../pos/posApi';

// Interface definitions for invoice printing
interface CompanyInfo {
  uuid: string;
  name: string;
  nif: string;
  rc: string;
  articleNumber: string;
  address: string;
  phoneNumber: string;
  website?: string;
}

interface CustomerInfo {
  uuid: string;
  name: string;
  fiscalId: string;
  email?: string;
  phone?: string;
  address?: string;
  rc?: string;
  articleNumber?: string;
  customerType: 'retail' | 'wholesale' | 'mid-wholesale' | 'institutional';
}

export const useSalesActions = (refetchSales?: () => void) => {
  const { user } = useAuth();
  
  // Modal states
  const [selectedSale, setSelectedSale] = useState<Sale | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [isInvoiceModalOpen, setIsInvoiceModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [saleToCancel, setSaleToCancel] = useState<Sale | null>(null);
  
  // Loading states
  const [isLoadingInvoice, setIsLoadingInvoice] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [isLoadingEditSale, setIsLoadingEditSale] = useState(false);
  const [isLoadingViewDetails, setIsLoadingViewDetails] = useState(false);
  const [isLoadingPrintInvoice, setIsLoadingPrintInvoice] = useState(false);
  const [isLoadingCancel, setIsLoadingCancel] = useState(false);
  
  // Invoice data
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);

  // POS edit sale data - fetched at list level
  const [editSaleData, setEditSaleData] = useState<Sale | null>(null);

  // View sale details - with loading state
  const handleViewDetails = async (sale: Sale) => {
    
    setIsLoadingViewDetails(true);
    try {
      
      const detailedSale = await getSale(sale.uuid);
      
      setSelectedSale(detailedSale);
      setIsDetailsModalOpen(true);
      toast.success('Sale details loaded');
      
    } catch (error) {
      toast.error('Failed to load sale details');
    } finally {
      setIsLoadingViewDetails(false);
    }
  };

  // Edit sale - fetch data first, then switch to POS view
  const handleEdit = async (sale: Sale) => {
    
    setIsLoadingEditSale(true);
    try {
      // Fetch the complete sale data first
      
      const detailedSale = await getSale(sale.uuid);
      
      // Load customer data if needed (similar to POS loadSale function)
      if (detailedSale.customerUuidString && user?.warehouseUuid) {
        try {
          
          const customersResponse = await filterCustomers({ warehouseUuid: user.warehouseUuid });
          const customer = customersResponse.data.find((c: any) => c.uuid === detailedSale.customerUuidString);
          if (customer) {
            (detailedSale as any).customer = customer;
          } else {
            console.warn('[useSalesActions] Customer not found:', detailedSale.customerUuidString);
          }
        } catch (error) {
          console.error('[useSalesActions] Failed to load customer data:', error);
        }
      }
      
      // Store the fetched data for POS view
      setEditSaleData(detailedSale);
      
      // Update URL parameters to switch to POS view (no need for sale UUID in URL)
      updateUrlParams({ 
        view: 'pos' 
      });
      
      toast.success('Sale loaded for editing');
      
    } catch (error) {
      toast.error('Failed to load sale for editing');
    } finally {
      setIsLoadingEditSale(false);
    }
  };
    
  // Clear edit sale data when switching away from POS
  const clearEditSaleData = () => {
    setEditSaleData(null);
  };

  // Print invoice
  const handlePrintInvoice = async (sale: Sale) => {
    if (!user?.uuid) {
      toast.error('User information missing');
      return;
    }

    setIsLoadingPrintInvoice(true);
    try {
      
      // Fetch the complete sale data to ensure we have all tax information
      const detailedSale = await getSale(sale.uuid);
      
      // Fetch company information
      
      const companies = await getCompaniesByUser(user.uuid);
      if (companies.length === 0) {
        toast.error('No company information found for this user');
        return;
      }
      const company = companies[0];
      
      // Transform company data to match CompanyInfo interface
      const companyInfo: CompanyInfo = {
        uuid: company.uuid,
        name: company.name,
        nif: company.nif,
        rc: company.rc,
        articleNumber: company.articleNumber,
        address: company.address,
        phoneNumber: company.phoneNumber,
        website: company.website
      };
      
      // Fetch customer information
      
      const customer = await getCustomerByUuid(detailedSale.customerUuidString);
      
      // Transform customer data to match CustomerInfo interface
      const customerInfo: CustomerInfo = {
        uuid: customer.uuid,
        name: customer.name,
        fiscalId: customer.fiscalId || 'N/A',
        email: customer.email,
        phone: customer.phone,
        address: customer.address,
        rc: customer.rc,
        articleNumber: customer.articleNumber,
        customerType: customer.customerType
      };
      
      setCompanyInfo(companyInfo);
      setCustomerInfo(customerInfo);
      setSelectedSale(detailedSale);
      setIsInvoiceModalOpen(true);
      
    } catch (error: any) {
      if (error.response?.status === 401) {
        toast.error('Authentication error. Please log in again.');
      } else {
        toast.error('Error loading invoice data');
      }
    } finally {
      setIsLoadingPrintInvoice(false);
    }
  };

  // Cancel sale - with loading state
  const handleCancel = async (sale: Sale) => {
    if (sale.status === 'cancelled') {
      toast.error('Sale is already cancelled');
      return;
    }
    
    
    setIsLoadingCancel(true);
    try {
      // Set the sale to cancel and open modal
    setSaleToCancel(sale);
    setIsCancelModalOpen(true);
      
    } catch (error) {
      toast.error('Failed to open cancel dialog');
    } finally {
      setIsLoadingCancel(false);
    }
  };

  const handleConfirmCancel = async () => {
    if (!saleToCancel || !user?.uuid) return;

    setIsCancelling(true);
    try {
      await cancelSale(saleToCancel.uuid, user.uuid);
      toast.success('Sale cancelled successfully');
      if (refetchSales) {
        refetchSales();
      }
      setIsCancelModalOpen(false);
      setSaleToCancel(null);
    } catch (error) {
      toast.error('Failed to cancel sale');
    } finally {
      setIsCancelling(false);
    }
  };

  const handleCancelModalClose = () => {
    setIsCancelModalOpen(false);
    setSaleToCancel(null);
  };

  // Close modals
  const closeDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedSale(null);
  };

  const closeInvoiceModal = () => {
    setIsInvoiceModalOpen(false);
    setSelectedSale(null);
    setCompanyInfo(null);
    setCustomerInfo(null);
  };

  return {
    // State
    selectedSale,
    isDetailsModalOpen,
    isInvoiceModalOpen,
    isCancelModalOpen,
    saleToCancel,
    isLoadingInvoice,
    isCancelling,
    companyInfo,
    customerInfo,
    editSaleData,
    isLoadingEditSale,
    isLoadingViewDetails,
    isLoadingPrintInvoice,
    isLoadingCancel,
    
    // Actions
    handleViewDetails,
    handleEdit,
    handlePrintInvoice,
    handleCancel,
    handleConfirmCancel,
    handleCancelModalClose,
    clearEditSaleData,
    closeDetailsModal,
    closeInvoiceModal
  };
}; 