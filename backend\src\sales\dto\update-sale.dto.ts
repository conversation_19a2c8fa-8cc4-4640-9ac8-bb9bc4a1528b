import {
  <PERSON>S<PERSON>,
  IsBoolean,
  IsNumber,
  IsOptional,
  IsUUID,
  Min,
  Max,
  IsArray,
  ValidateNested,
  IsEnum,
  IsDateString,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { SaleStatus, PaymentMethods } from "../sale.entity";
import { SaleItemDto } from "./create-sale.dto";

export class UpdateSaleDto {
  @ApiProperty({
    description: "UUID of the user updating the sale",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
  })
  @IsString()
  @IsUUID('all', { message: "userUuid must be a valid UUID" })
  userUuid!: string;

  @ApiProperty({
    description: "UUID of the customer for the sale",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID('all', { message: "customerUuid must be a valid UUID" })
  customerUuid?: string;

  @ApiProperty({
    description: "Customer name at time of sale",
    example: "Customer Name",
    required: false,
  })
  @IsOptional()
  @IsString()
  customerName?: string;

  @ApiProperty({
    description: "Customer fiscal ID at time of sale",
    example: "12345678901",
    required: false,
  })
  @IsOptional()
  @IsString()
  customerFiscalId?: string;

  @ApiProperty({
    description: "Customer RC at time of sale",
    example: "RC123456",
    required: false,
  })
  @IsOptional()
  @IsString()
  customerRc?: string;

  @ApiProperty({
    description: "Customer article number at time of sale",
    example: "ART001",
    required: false,
  })
  @IsOptional()
  @IsString()
  customerArticleNumber?: string;

  @ApiProperty({
    description: "UUID of the order this sale was created from",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID('all', { message: "orderUuid must be a valid UUID" })
  orderUuid?: string;

  @ApiProperty({
    description: "List of items in the sale (optional for updates)",
    type: [SaleItemDto],
    example: [
      {
        productUuid: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
        name: "Sample Product",
        quantity: 2,
        unitPrice: 25.50
      }
    ],
    required: false,
  })
  @IsOptional()
  @IsArray({ message: "items must be an array" })
  @ValidateNested({ each: true })
  @Type(() => SaleItemDto)
  items?: SaleItemDto[];

  @ApiProperty({
    description: "Subtotal amount before tax",
    example: 150.0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: "subtotal must be a number" })
  @Min(0, { message: "subtotal must be at least 0" })
  subtotal?: number;

  @ApiProperty({
    description: "Whether tax is applied to this sale",
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  useTax?: boolean;

  @ApiProperty({
    description: "Tax rate as decimal (e.g., 0.1 for 10%)",
    example: 0.1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: "Tax rate must be a number" })
  @Min(0, { message: "Tax rate must be at least 0" })
  @Max(1, { message: "Tax rate must be at most 1" })
  taxRate?: number;

  @ApiProperty({
    description: "Total tax amount",
    example: 15.0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: "taxAmount must be a number" })
  @Min(0, { message: "taxAmount must be at least 0" })
  taxAmount?: number;

  @ApiProperty({
    description: "Total amount including tax",
    example: 165.0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: "totalAmount must be a number" })
  @Min(0, { message: "totalAmount must be at least 0" })
  totalAmount?: number;

  @ApiProperty({
    description: "Amount paid so far",
    example: 100.0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: "amountPaid must be a number" })
  @Min(0, { message: "amountPaid must be at least 0" })
  amountPaid?: number;

  @ApiProperty({
    description: "Balance due",
    example: 65.0,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: "balanceDue must be a number" })
  @Min(0, { message: "balanceDue must be at least 0" })
  balanceDue?: number;

  @ApiProperty({
    description: "Payment method used",
    enum: PaymentMethods,
    example: PaymentMethods.CASH,
    required: false,
  })
  @IsOptional()
  @IsEnum(PaymentMethods, { message: "paymentMethod must be a valid payment method" })
  paymentMethod?: PaymentMethods;

  // Removed paymentDate field since it's redundant with updatedAt and excluded from logs

  @ApiProperty({
    description: "Invoice date",
    example: "2025-01-15T10:30:00.000Z",
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: "invoiceDate must be a valid date string" })
  invoiceDate?: string;

  @ApiProperty({
    description: "Due date for payment",
    example: "2025-02-14T10:30:00.000Z",
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: "dueDate must be a valid date string" })
  dueDate?: string;

  @ApiProperty({
    description: "Current status of the sale",
    enum: SaleStatus,
    example: SaleStatus.UNPAID,
    required: false,
  })
  @IsOptional()
  @IsEnum(SaleStatus, { message: "status must be a valid sale status" })
  status?: SaleStatus;

  @ApiProperty({
    description: "UUID of the user who last updated the sale",
    example: "018ea2bb-6b8d-7e9a-8a2a-7e9a8a2a7e9a",
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsUUID('all', { message: "updatedBy must be a valid UUID" })
  updatedBy?: string;
}
