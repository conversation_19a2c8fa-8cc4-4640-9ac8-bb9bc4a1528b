// Constants for filtering logic in Purchases

export const EMPTY_STRING_FILTER = "";
export const EMPTY_UUID_FILTER = "";

export const MIN_DATE_FILTER = new Date("1900-01-01T00:00:00.000Z");
export const MAX_DATE_FILTER = new Date("2100-12-31T23:59:59.999Z");

export const MIN_NUMBER_FILTER = Number.MIN_SAFE_INTEGER;
export const MAX_NUMBER_FILTER = Number.MAX_SAFE_INTEGER;

export const RETURN_REASONS = {
  DAMAGED_GOODS: "damaged_goods",
  WRONG_ITEM: "wrong_item",
  QUALITY_ISSUE: "quality_issue",
  EXPIRED: "expired",
  OVERSUPPLIED: "oversupplied",
  OTHER: "other",
} as const;

export const PURCHASE_STATUS_FLOW = {
  DRAFT: ["PENDING_APPROVAL", "CANCELLED"],
  PENDING_APPROVAL: ["APPROVED", "REJECTED", "CANCELLED"],
  APPROVED: ["ORDERED", "CANCELLED"],
  ORDERED: ["PARTIALLY_RECEIVED", "RECEIVED", "CANCELLED"],
  PARTIALLY_RECEIVED: ["RECEIVED", "CANCELLED"],
  RECEIVED: ["UNPAID", "PARTIALLY_PAID", "PAID"],
  FULLY_RECEIVED: ["UNPAID", "PARTIALLY_PAID", "PAID"],
  UNPAID: ["PARTIALLY_PAID", "PAID"],
  PARTIALLY_PAID: ["PAID"],
  PAID: [],
  CANCELLED: [],
  RETURNED: [],
} as const; 