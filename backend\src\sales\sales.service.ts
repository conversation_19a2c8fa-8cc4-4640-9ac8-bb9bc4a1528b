import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, ILike, In } from "typeorm";
import { UsersService } from "../users/users.service";
import { Sale, SaleStatus, PaymentMethods } from "./sale.entity";
import { SaleItem } from "./sale-item.entity";
import { Customer } from "../customers/customer.entity";
import { Product } from "../products/product.entity";
import { 
  EMPTY_STRING_FILTER, 
  EMPTY_UUID_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
} from "./sales.constants";
import {
  SaleResponseDto,
  toSaleResponseDto,
  SalesListResponseDto,
} from "./dto/sales-response.dto";
import { InventoryService } from "../inventory/inventory.service.typeorm";

@Injectable()
export class SalesService {
  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
    @InjectRepository(SaleItem)
    private saleItemRepository: Repository<SaleItem>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    private usersService: UsersService,
    private inventoryService: InventoryService,
  ) {}

  /**
   * Utility function to round numbers to 2 decimal places
   * This ensures consistent precision for all monetary calculations
   */
  private roundToTwoDecimals(value: number): number {
    return Math.round(value * 100) / 100;
  }

  /**
   * Create a new Sale with required validation for customer and items.
   */
  async create(
    userUuid: string,
    warehouseUuid: string,
    customerUuid: string | undefined,
    items: any[] | undefined,
    useTax: boolean = false,
    taxRate: number = 0.1,
    status: SaleStatus = SaleStatus.UNPAID,
    paymentMethod?: PaymentMethods,
    amountPaid?: number,
  ) {
    const now = new Date();

    // Validate required fields
    if (!userUuid || !warehouseUuid) {
      throw new BadRequestException(
        "userUuid and warehouseUuid are required to create a sale",
      );
    }

    if (!items || items.length === 0) {
      throw new BadRequestException("At least one item is required to create a sale");
    }

    // Check user exists
    const user = await this.usersService.findOne(userUuid);
    if (!user) throw new NotFoundException("User not found");

    // Check customer exists if provided
    let customer: Customer | null = null;
    if (customerUuid) {
      customer = await this.customerRepository.findOne({
        where: { id: customerUuid, isDeleted: false }
      });
      if (!customer) throw new NotFoundException("Customer not found");
    }

    // Validate items and get product information
    const validatedItems = await this.validateAndTransformItems(items);

    // Calculate totals
    const subtotal = this.roundToTwoDecimals(
      validatedItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );
    const taxAmount = useTax ? this.calculateTaxAmount(subtotal, taxRate) : 0;
    const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);
    const finalAmountPaid = amountPaid || 0;
    
    // Prevent overpayment
    if (finalAmountPaid > totalAmount) {
      throw new BadRequestException(
        `Payment amount (${finalAmountPaid}) cannot exceed total sale amount (${totalAmount}). Overpayment is not allowed.`
      );
    }
    
    const balanceDue = this.roundToTwoDecimals(totalAmount - finalAmountPaid);

    // Generate invoice number
    const timestamp = now.getTime();
    const invoiceNumber = `INV-${timestamp}`;

    // Create sale entity
    const sale = this.saleRepository.create({
      id: Sale.generateId(),
      invoiceNumber,
      customerUuid: customerUuid || null,
      customerName: customer?.name || null,
      customerFiscalId: customer?.fiscalId || null,
      customerRc: customer?.rc || null,
      customerArticleNumber: customer?.articleNumber || null,
      subtotal,
      useTax,
      taxRate,
      taxAmount,
      totalAmount,
      amountPaid: finalAmountPaid,
      balanceDue,
      paymentMethod: paymentMethod || PaymentMethods.CASH,
      invoiceDate: now,
      dueDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      status,
      createdBy: userUuid,
      updatedBy: userUuid,
      isDeleted: false,
    });

    // Save sale
    const savedSale = await this.saleRepository.save(sale);

    // Create sale items
    const saleItems = validatedItems.map(item => 
      this.saleItemRepository.create({
        id: SaleItem.generateId(),
        saleUuid: savedSale.id,
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
        taxAmount: item.taxAmount || 0,
        order: item.order || 0, // Include the order field
      })
    );

    await this.saleItemRepository.save(saleItems);

    // Handle stock adjustments if not cancelled
    if (status !== SaleStatus.CANCELLED) {
      const storageUuid = await this.findDefaultWarehouseStorage(warehouseUuid);
      await this.createStockAdjustmentsForSale(
        validatedItems,
        storageUuid,
        userUuid,
        warehouseUuid,
        `Sale transaction - Invoice ${invoiceNumber}`
      );
    }

    // Return the created sale
    return this.findOne(savedSale.id);
  }

  /**
   * Calculate tax amount based on subtotal and tax rate
   */
  private calculateTaxAmount(subtotal: number, taxRate: number): number {
    return this.roundToTwoDecimals(subtotal * taxRate);
  }

  /**
   * Find the default storage for a warehouse
   */
  private async findDefaultWarehouseStorage(
    warehouseUuid: string,
  ): Promise<string> {
    // TODO: Implement proper storage lookup
    // This would need to be implemented based on your storage structure
    return warehouseUuid; // This should be the actual storage UUID
  }

  /**
   * Validate stock availability for sale items
   */
  private async validateStockAvailability(
    items: any[],
    storageUuid: string,
  ): Promise<void> {
    // TODO: Implement stock validation
    // This would need to be implemented based on your inventory structure
  }

  /**
   * Create stock adjustments for sale
   */
  private async createStockAdjustmentsForSale(
    items: any[],
    storageUuid: string,
    userUuid: string,
    warehouseUuid: string,
    reason: string = "Sale transaction",
  ): Promise<void> {
    // TODO: Implement stock adjustments
    // This would need to be implemented based on your inventory structure
  }

  /**
   * Find all sales with filtering and pagination
   */
  async findAll({
    customerUuid = EMPTY_UUID_FILTER,
    status = EMPTY_STRING_FILTER,
    createdFrom,
    createdTo,
    page = 1,
    limit = 10,
    warehouseUuid,
    invoiceNumber,
    paymentMethod,
    startDate,
    endDate,
    minAmount = MIN_NUMBER_FILTER,
    maxAmount = MAX_NUMBER_FILTER,
  }: {
    customerUuid?: string;
    status?: string;
    createdFrom?: Date;
    createdTo?: Date;
    page?: number;
    limit?: number;
    warehouseUuid?: string;
    invoiceNumber?: string;
    paymentMethod?: string;
    startDate?: Date;
    endDate?: Date;
    minAmount?: number;
    maxAmount?: number;
  } = {}): Promise<SalesListResponseDto> {
    const queryBuilder = this.saleRepository
      .createQueryBuilder('sale')
      .leftJoinAndSelect('sale.saleItems', 'saleItems')
      .where('sale.isDeleted = :isDeleted', { isDeleted: false });

    // Apply filters
    if (customerUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere('sale.customerUuid = :customerUuid', { customerUuid });
    }

    if (status !== EMPTY_STRING_FILTER) {
      queryBuilder.andWhere('sale.status = :status', { status });
    }

    if (createdFrom) {
      queryBuilder.andWhere('sale.createdAt >= :createdFrom', { createdFrom });
    }

    if (createdTo) {
      queryBuilder.andWhere('sale.createdAt <= :createdTo', { createdTo });
    }

    if (invoiceNumber) {
      queryBuilder.andWhere('sale.invoiceNumber ILIKE :invoiceNumber', { 
        invoiceNumber: `%${invoiceNumber}%` 
      });
    }

    if (paymentMethod) {
      queryBuilder.andWhere('sale.paymentMethod = :paymentMethod', { paymentMethod });
    }

    if (startDate) {
      queryBuilder.andWhere('sale.invoiceDate >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('sale.invoiceDate <= :endDate', { endDate });
    }

    if (minAmount !== MIN_NUMBER_FILTER) {
      queryBuilder.andWhere('sale.totalAmount >= :minAmount', { minAmount });
    }

    if (maxAmount !== MAX_NUMBER_FILTER) {
      queryBuilder.andWhere('sale.totalAmount <= :maxAmount', { maxAmount });
    }

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Order by creation date (newest first)
    queryBuilder.orderBy('sale.createdAt', 'DESC');

    // Get the sales without items first to get the total count
    const [salesWithoutItems, total] = await queryBuilder.getManyAndCount();
    
    // Then load the sales with their items using the repository
    const sales = await this.saleRepository.find({
      where: { id: In(salesWithoutItems.map(s => s.id)) },
      relations: ['saleItems'],
      order: { createdAt: 'DESC' },
    });



    // Transform to response DTOs
    const saleDtos = await this.enrichSalesData(sales);

    return new SalesListResponseDto(
      saleDtos,
      total,
      page,
      limit,
      Math.ceil(total / limit)
    );
  }

  /**
   * Find sales by warehouse
   */
  async findByWarehouse(warehouseUuid: string): Promise<SaleResponseDto[]> {
    const sales = await this.saleRepository.find({
      where: { isDeleted: false },
      relations: ['saleItems'],
      order: { createdAt: 'DESC' },
    });

    return this.enrichSalesData(sales);
  }

  /**
   * Find one sale by UUID
   */
  async findOne(uuid: string): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['saleItems'],
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    const enrichedSales = await this.enrichSalesData([sale]);
    return enrichedSales[0];
  }

  /**
   * Enrich sales data with additional information
   */
  private async enrichSalesData(
    sales: Sale[],
  ): Promise<SaleResponseDto[]> {
    // Get all unique UUIDs for batch queries
    const customerUuids = [...new Set(sales.map(s => s.customerUuid).filter(Boolean))];
    const createdByUuids = [...new Set(sales.map(s => s.createdBy))];
    const updatedByUuids = [...new Set(sales.map(s => s.updatedBy))];
    
    // Get all product UUIDs from sale items
    const productUuids = [...new Set(
      sales.flatMap(sale => 
        sale.saleItems?.map(item => item.productUuid).filter(Boolean) || []
      )
    )];

    // Batch fetch related data
    const [customers, users, products] = await Promise.all([
      customerUuids.length > 0 
        ? this.customerRepository.find({ where: { id: In(customerUuids) } })
        : [],
      createdByUuids.length > 0 || updatedByUuids.length > 0
        ? Promise.all([
            ...createdByUuids.map(uuid => this.usersService.findOne(uuid)),
            ...updatedByUuids.map(uuid => this.usersService.findOne(uuid)),
          ])
        : [],
      productUuids.length > 0
        ? this.productRepository.find({ where: { id: In(productUuids) } })
        : [],
    ]);

    // Create lookup maps with proper typing
    const customerMap = new Map<string, any>();
    customers.forEach(c => customerMap.set(c.id, c));
    
    const userMap = new Map<string, any>();
    users.forEach((user: any) => {
      if (user) userMap.set(user.id, user);
    });
    
    const productMap = new Map<string, any>();
    products.forEach(p => productMap.set(p.id, p));

    // Transform sales to DTOs
    return sales.map(sale => {
      const customer = sale.customerUuid ? customerMap.get(sale.customerUuid) : null;
      const createdByUser = userMap.get(sale.createdBy);
      const updatedByUser = userMap.get(sale.updatedBy);

      // Create a map of product UUIDs to product names
      const productNameMap = new Map<string, string>();
      products.forEach(p => productNameMap.set(p.id, p.name));

      // Create the base sale response DTO
      const saleResponse = toSaleResponseDto(
        sale,
        customer?.name || "Unknown Customer",
        createdByUser?.name || "Unknown User",
        updatedByUser?.name || "Unknown User",
        productNameMap
      );
      
      // Add enriched data as additional properties
      return {
        ...saleResponse,
        customer: customer ? {
          uuid: customer.id,
          name: customer.name,
          fiscalId: customer.fiscalId,
          rc: customer.rc,
          articleNumber: customer.articleNumber,
        } : null,
        createdByUser: createdByUser ? {
          uuid: createdByUser.id,
          name: createdByUser.name,
          email: createdByUser.email,
        } : null,
        updatedByUser: updatedByUser ? {
          uuid: updatedByUser.id,
          name: updatedByUser.name,
          email: updatedByUser.email,
        } : null,
      };
    });
  }

  /**
   * Update a sale
   */
  async update(uuid: string, data: any, userUuid: string): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    // Check if payment-related fields are being updated
    const isPaymentUpdate = data.amountPaid !== undefined || data.paymentMethod !== undefined;
    
    // Check if tax-related fields are being updated
    const isTaxUpdate = data.useTax !== undefined || data.taxRate !== undefined;
    
    // Check if items are being updated
    const isItemsUpdate = data.items !== undefined && Array.isArray(data.items);
    
    // Extract items early so we can use them consistently
    const updateItems = data.items;
    
    // Update sale fields (excluding items which are handled separately)
    const { items: _, ...updateData } = data;
    Object.assign(sale, updateData);
    sale.updatedAt = new Date();

    // If items are being updated, replace all items
    if (isItemsUpdate) {
      try {
        // Delete existing items
        await this.saleItemRepository.delete({ saleUuid: sale.id });
        
        // Validate and transform new items
        const validatedItems = await this.validateAndTransformItems(updateItems);
        
        // Create new sale items
        const saleItems = validatedItems.map(item => 
          this.saleItemRepository.create({
            id: SaleItem.generateId(),
            saleUuid: sale.id,
            productUuid: item.productUuid,
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            lineTotal: item.lineTotal,
            taxAmount: item.taxAmount || 0,
            order: item.order || 0, // Preserve the order field
          })
        );
        
        await this.saleItemRepository.save(saleItems);
      } catch (error) {
        throw new BadRequestException(`Failed to update sale items: ${error.message}`);
      }
    }

    // IMPORTANT: Recalculate totals BEFORE payment validation
    // If tax is being updated or items are being updated, recalculate totals
    if (isTaxUpdate || isItemsUpdate) {
      try {
        let saleItems: any[];
        
        if (isItemsUpdate && updateItems && updateItems.length > 0) {
          // Use the NEW items that were sent in the update request
          saleItems = updateItems;
        } else if (isItemsUpdate) {
          // Items update was requested but no items provided
          throw new BadRequestException('Items update requested but no items provided. Please include the updated items in the request.');
        } else {
          // Fallback to current sale items from database (for tax-only updates)
          saleItems = await this.saleItemRepository.find({
            where: { saleUuid: sale.id },
          });
        }
        
        if (saleItems.length === 0) {
          throw new BadRequestException('Cannot update sale: No sale items found. The sale appears to be empty.');
        }
        
        // Recalculate subtotal with validation
        let subtotal = 0;
        try {
          subtotal = this.roundToTwoDecimals(
            saleItems.reduce((sum, item) => {
              // Handle both lineTotal (from database) and totalPrice (from frontend)
              const itemTotal = item.lineTotal || item.totalPrice;
              
              if (itemTotal === null || itemTotal === undefined) {
                throw new BadRequestException(`Item "${item.name}" has no line total. Please check the sale items data.`);
              }
              
              let lineTotal: number;
              if (typeof itemTotal === 'string') {
                lineTotal = parseFloat(itemTotal);
                if (isNaN(lineTotal)) {
                  throw new BadRequestException(`Item "${item.name}" has invalid line total string: "${itemTotal}". Cannot parse to number.`);
                }
              } else if (typeof itemTotal === 'number') {
                lineTotal = itemTotal;
              } else {
                throw new BadRequestException(`Item "${item.name}" has invalid line total type: ${typeof itemTotal}. Expected a number or string.`);
              }
              
              if (isNaN(lineTotal)) {
                throw new BadRequestException(`Item "${item.name}" has invalid line total: "${itemTotal}". Expected a valid number.`);
              }
              
              if (lineTotal < 0) {
                throw new BadRequestException(`Item "${item.name}" has negative line total: ${lineTotal}. Line totals cannot be negative.`);
              }
              
              return sum + lineTotal;
            }, 0)
          );
        } catch (error) {
          if (error instanceof BadRequestException) {
            throw error; // Re-throw our custom error
          }
          throw new BadRequestException(`Failed to calculate sale subtotal: ${error.message}`);
        }
        
        // Validate subtotal
        if (isNaN(subtotal)) {
          throw new BadRequestException('Invalid sale items - cannot calculate subtotal');
        }
        
        // Recalculate tax amount
        let taxAmount = 0;
        if (sale.useTax) {
          if (sale.taxRate === null || sale.taxRate === undefined || isNaN(sale.taxRate)) {
            throw new BadRequestException(`Invalid tax rate: ${sale.taxRate}. Tax rate must be a valid number.`);
          }
          if (sale.taxRate < 0 || sale.taxRate > 1) {
            throw new BadRequestException(`Tax rate ${sale.taxRate} is out of range. Must be between 0 and 1.`);
          }
          taxAmount = this.calculateTaxAmount(subtotal, sale.taxRate);
        }
        
        // Recalculate total amount
        const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);
        
        // Validate total amount
        if (isNaN(totalAmount)) {
          throw new BadRequestException('Invalid calculation - cannot determine total amount');
        }
        
        // Update the sale with recalculated values
        sale.subtotal = subtotal;
        sale.taxAmount = taxAmount;
        sale.totalAmount = totalAmount;
      } catch (error) {
        throw new BadRequestException(`Failed to recalculate sale totals: ${error.message}`);
      }
    }

    // NOW validate payment amount AFTER totals are recalculated
    // This ensures we validate against the correct total amount after any item/tax changes
    if (isPaymentUpdate) {
      try {
        // Validate amount paid
        if (data.amountPaid !== undefined) {
          if (data.amountPaid < 0) {
            throw new BadRequestException("Amount paid cannot be negative");
          }
          
          // Prevent overpayment - amount paid cannot exceed total amount
          if (data.amountPaid > sale.totalAmount) {
            throw new BadRequestException(
              `Payment amount (${data.amountPaid}) cannot exceed total sale amount (${sale.totalAmount}). Overpayment is not allowed.`
            );
          }
        }
        
        const balanceDue = this.roundToTwoDecimals(sale.totalAmount - sale.amountPaid);
        
        if (isNaN(balanceDue)) {
          throw new BadRequestException('Invalid calculation - cannot determine balance due');
        }
        sale.balanceDue = balanceDue;
        
        // Update status based on payment
        if (sale.balanceDue <= 0) {
          sale.status = SaleStatus.PAID;
        } else if (sale.amountPaid > 0) {
          sale.status = SaleStatus.PARTIALLY_PAID;
        } else {
          sale.status = SaleStatus.UNPAID;
        }
        
      } catch (error) {
        throw error; // Re-throw the error as it's already a BadRequestException
      }
    }

    try {
      const updatedSale = await this.saleRepository.save(sale);
      const result = await this.findOne(updatedSale.id);
      return result;
    } catch (error) {
      // Check if it's a NaN error
      if (error.message && error.message.includes('DECIMAL does not support NaN')) {
        throw new BadRequestException(
          'Sale update failed: Invalid numeric values detected. This usually means the sale items have corrupted data. ' +
          'Please check the sale items and try again, or contact support if the problem persists.'
        );
      }
      
      throw new BadRequestException(`Failed to save sale update: ${error.message}`);
    }
  }

  /**
   * Update sale status
   */
  async updateSaleStatus(
    uuid: string,
    newStatus: SaleStatus,
    userUuid: string,
  ): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    sale.status = newStatus;
    sale.updatedAt = new Date();
    sale.updatedBy = userUuid;

    const updatedSale = await this.saleRepository.save(sale);
    return this.findOne(updatedSale.id);
  }

  /**
   * Remove a sale (soft delete)
   */
  async remove(uuid: string): Promise<{ message: string }> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    sale.isDeleted = true;
    sale.updatedAt = new Date();
    await this.saleRepository.save(sale);

    return { message: "Sale deleted successfully" };
  }

  /**
   * Add products to a sale
   */
  async addProducts(uuid: string, items: any[]): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    // Validate and transform items
    const validatedItems = await this.validateAndTransformItems(items);

    // Create new sale items
    const saleItems = validatedItems.map(item => 
      this.saleItemRepository.create({
        id: SaleItem.generateId(),
        saleUuid: sale.id,
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
        taxAmount: item.taxAmount || 0,
        order: item.order || 0, // Preserve the order field
      })
    );

    await this.saleItemRepository.save(saleItems);

    // Recalculate totals
    const allItems = await this.saleItemRepository.find({
      where: { saleUuid: sale.id },
    });

    const subtotal = this.roundToTwoDecimals(
      allItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );
    const taxAmount = sale.useTax ? this.calculateTaxAmount(subtotal, sale.taxRate) : 0;
    const totalAmount = this.roundToTwoDecimals(subtotal + taxAmount);
    const balanceDue = this.roundToTwoDecimals(totalAmount - sale.amountPaid);

    sale.subtotal = subtotal;
    sale.taxAmount = taxAmount;
    sale.totalAmount = totalAmount;
    sale.balanceDue = balanceDue;
    sale.updatedAt = new Date();

    await this.saleRepository.save(sale);

    return this.findOne(sale.id);
  }

  /**
   * Validate and transform items
   */
  private async validateAndTransformItems(items: any[]): Promise<any[]> {
    const productUuids = items.map(item => item.productUuid);
    const products = await this.productRepository.find({
      where: { id: In(productUuids), isDeleted: false },
    });

    const productMap = new Map(products.map(p => [p.id, p]));

    // Sort items by order to preserve cart order, then validate and transform
    return items
      .sort((a, b) => (a.order || 0) - (b.order || 0))
      .map(item => {
        const product = productMap.get(item.productUuid);
        if (!product) {
          throw new BadRequestException(`Product with UUID ${item.productUuid} not found`);
        }

        const quantity = parseFloat(item.quantity);
        const unitPrice = parseFloat(item.unitPrice);
        const calculatedLineTotal = this.roundToTwoDecimals(quantity * unitPrice);
        
        // Check if frontend provided totalPrice and if it matches our calculation
        let lineTotal = calculatedLineTotal;
        if (item.totalPrice !== undefined && item.totalPrice !== null) {
          const frontendTotalPrice = parseFloat(item.totalPrice);
          const difference = Math.abs(frontendTotalPrice - calculatedLineTotal);
          
          // If the difference is very small (rounding error), use frontend value
          if (difference < 0.01) {
            lineTotal = frontendTotalPrice;
          }
        }

        return {
          productUuid: item.productUuid,
          name: product.name,
          quantity,
          unitPrice,
          lineTotal,
          taxAmount: item.taxAmount || 0,
          order: item.order || 0, // Preserve the order field
        };
      });
  }

  /**
   * Set payment for a sale
   */
  async setPayment(uuid: string, paymentMethod: string, amountPaid: number): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    sale.paymentMethod = paymentMethod as PaymentMethods;
    sale.amountPaid = this.roundToTwoDecimals(amountPaid);
    sale.balanceDue = this.roundToTwoDecimals(sale.totalAmount - sale.amountPaid);
    // Removed paymentDate update since it's redundant with updatedAt and excluded from logs

    // Update status based on payment
    if (sale.balanceDue <= 0) {
      sale.status = SaleStatus.PAID;
    } else if (sale.amountPaid > 0) {
      sale.status = SaleStatus.PARTIALLY_PAID;
    }

    sale.updatedAt = new Date();
    await this.saleRepository.save(sale);

    return this.findOne(sale.id);
  }

  /**
   * Cancel a sale
   */
  async cancelSale(uuid: string, userUuid: string): Promise<SaleResponseDto> {
    const sale = await this.saleRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!sale) {
      throw new NotFoundException("Sale not found");
    }

    if (sale.status === SaleStatus.CANCELLED) {
      throw new BadRequestException("Sale is already cancelled");
    }

    sale.status = SaleStatus.CANCELLED;
    sale.updatedAt = new Date();
    sale.updatedBy = userUuid;

    await this.saleRepository.save(sale);

    return this.findOne(sale.id);
  }



} 