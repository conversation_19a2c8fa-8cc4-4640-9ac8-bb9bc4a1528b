"use client";
import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode, useRef } from "react";
import { useRouter } from "next/navigation";

// Enhanced User interface to match backend UserInfoDto
export interface User {
  uuid: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  phone: string | null;
  isActive: boolean;
  roles: UserRole[];
  warehouseUuid: string | null;
  warehouseUuidString?: string | null; // Backend field name
  vanUuid: string | null;
  createdAt: string;
  updatedAt: string;
  // Legacy support fields
  name?: string;
  avatarUrl?: string;
  userType?: 'super' | 'user';
  warehouseName?: string;
}

export interface UserRole {
  uuid: string;
  name: string;
  permissions: string[];
}

export interface SessionStats {
  activeTokens: number;
  totalTokens: number;
  lastUsed: string | null;
}





interface AuthContextType {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  loading: boolean;
  login: (user: User, token: string, refreshToken: string) => void;
  logout: () => void;
  refreshAccessToken: () => Promise<boolean>;
  getSecurityStatus: () => Promise<SecurityStatus | null>;
  fetchAndPersistWarehouseInfo: (userObj?: User, token?: string | null) => Promise<void>;
  checkUserExists: (uuid: string) => Promise<boolean>;
  switchWarehouse: (warehouseUuid: string, warehouseName: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [refreshToken, setRefreshToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [fetchingWarehouse, setFetchingWarehouse] = useState(false);
  const [refreshingToken, setRefreshingToken] = useState(false);
  const router = useRouter();
  
  // Use refs to avoid dependency issues
  const fetchingWarehouseRef = useRef(false);
  const tokenRef = useRef<string | null>(null);
  const userRef = useRef<User | null>(null);
  
  // Update refs when state changes
  useEffect(() => {
    fetchingWarehouseRef.current = fetchingWarehouse;
  }, [fetchingWarehouse]);
  
  useEffect(() => {
    tokenRef.current = token;
  }, [token]);
  
  useEffect(() => {
    userRef.current = user;
  }, [user]);

  // Helper to fetch warehouse info in background
  const fetchAndPersistWarehouseInfo = useCallback(async (userObjParam?: User, tokenParam?: string | null) => {
    const tokenToUse = tokenParam !== undefined ? tokenParam : tokenRef.current;
    const userToFetch = userObjParam || userRef.current;
    
    console.log('fetchAndPersistWarehouseInfo called with:', {
      tokenToUse: !!tokenToUse,
      userToFetch: userToFetch?.uuid,
      userToFetchWarehouseUuid: userToFetch?.warehouseUuidString || userToFetch?.warehouseUuid,
      fetchingWarehouse: fetchingWarehouseRef.current,
      currentUser: userRef.current?.uuid,
      currentUserWarehouseUuid: userRef.current?.warehouseUuidString || userRef.current?.warehouseUuid
    });
    
    if (!tokenToUse || !userToFetch?.uuid || fetchingWarehouseRef.current) {
      console.log('fetchAndPersistWarehouseInfo early return:', {
        noToken: !tokenToUse,
        noUserUuid: !userToFetch?.uuid,
        alreadyFetching: fetchingWarehouseRef.current
      });
      return;
    }
    
    // Additional safety check: ensure UUID is a valid string
    if (typeof userToFetch.uuid !== 'string' || userToFetch.uuid.trim() === '') {
      console.log('fetchAndPersistWarehouseInfo early return: invalid UUID:', userToFetch.uuid);
      return;
    }
    
    // Additional check: if we don't have a valid token in localStorage, don't proceed
    const storedToken = localStorage.getItem('dido_token');
    if (!storedToken) {
      console.log('fetchAndPersistWarehouseInfo early return: no stored token');
      return;
    }
    
    setFetchingWarehouse(true);
    try {
      console.log('Fetching latest user info for UUID:', userToFetch.uuid);
      // Always fetch the latest user info from backend to ensure we have current data
      const userRes = await fetch(`/api/users/${userToFetch.uuid}`, {
        headers: { Authorization: `Bearer ${tokenToUse}` },
      });
      if (!userRes.ok) {
        console.error('Failed to fetch user info:', userRes.status, userRes.statusText);
        return;
      }
      const latestUser = await userRes.json();
      console.log('Latest user info from API:', latestUser);
      console.log('Warehouse UUID from backend:', {
        warehouseUuidString: latestUser.warehouseUuidString,
        warehouseUuid: latestUser.warehouseUuid,
        finalWarehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid
      });
      
      // Check if user has a warehouse assigned
      const warehouseUuid = latestUser.warehouseUuidString || latestUser.warehouseUuid;
      if (!warehouseUuid) {
        console.log('No warehouse assigned to user, setting user without warehouse info');
        const updatedUser: User = {
          ...latestUser,
          warehouseUuid: null,
          warehouseName: null,
        };
        setUser(updatedUser);
        localStorage.setItem("dido_user", JSON.stringify(updatedUser));
        return;
      }
      
      console.log('Fetching warehouse info for UUID:', warehouseUuid);
      const warehouseRes = await fetch(`/api/warehouses/${warehouseUuid}`, {
        headers: { Authorization: `Bearer ${tokenToUse}` },
      });
      
      if (!warehouseRes.ok) {
        console.error('Failed to fetch warehouse info:', warehouseRes.status, warehouseRes.statusText);
        // If warehouse fetch fails, still update user with current data but without warehouse name
        const updatedUser: User = {
          ...latestUser,
          warehouseUuid: warehouseUuid,
          warehouseName: null, // Warehouse name unknown
        };
        setUser(updatedUser);
        localStorage.setItem("dido_user", JSON.stringify(updatedUser));
        return;
      }
      
      const warehouse = await warehouseRes.json();
      console.log('Warehouse info from API:', warehouse);
      
      const updatedUser: User = {
        ...latestUser,
        warehouseUuid: warehouseUuid,
        warehouseName: warehouse.name,
      };
      
      console.log('Setting updated user with warehouse info:', updatedUser);
      setUser(updatedUser);
      localStorage.setItem("dido_user", JSON.stringify(updatedUser));
    } catch (err) {
      console.error('Error fetching warehouse info:', err);
      // On error, still try to update user with current data from backend
      try {
        const userRes = await fetch(`/api/users/${userToFetch.uuid}`, {
          headers: { Authorization: `Bearer ${tokenToUse}` },
        });
        if (userRes.ok) {
          const latestUser = await userRes.json();
          const updatedUser: User = {
            ...latestUser,
            warehouseUuid: latestUser.warehouseUuidString || latestUser.warehouseUuid,
            warehouseName: null, // Could not fetch warehouse name due to error
          };
          setUser(updatedUser);
          localStorage.setItem("dido_user", JSON.stringify(updatedUser));
        }
      } catch (fallbackErr) {
        console.error('Error in fallback user update:', fallbackErr);
      }
    } finally {
      setFetchingWarehouse(false);
    }
  }, []); // Remove all dependencies since we're using refs

  // Refresh access token using refresh token
  const refreshAccessToken = useCallback(async (): Promise<boolean> => {
    if (!refreshToken || refreshingToken) return false;
    
    setRefreshingToken(true);
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken,
          clientIp: 'frontend', // Will be overridden by backend
          userAgent: navigator.userAgent,
        }),
      });

      if (!response.ok) {
        console.error('Token refresh failed:', response.status);
        return false;
      }

      const data = await response.json();
      setToken(data.accessToken);
      setRefreshToken(data.refreshToken);
      localStorage.setItem("dido_token", data.accessToken);
      localStorage.setItem("dido_refresh_token", data.refreshToken);
      
      console.log('Token refreshed successfully');
      return true;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return false;
    } finally {
      setRefreshingToken(false);
    }
  }, [refreshToken, refreshingToken]);



  // On initial mount: restore session from localStorage
  useEffect(() => {
    console.log('AuthContext initial mount - checking localStorage');
    const storedUser = localStorage.getItem("dido_user");
    const storedToken = localStorage.getItem("dido_token");
    const storedRefreshToken = localStorage.getItem("dido_refresh_token");
    
    console.log('Stored data from localStorage:', {
      hasStoredUser: !!storedUser,
      hasStoredToken: !!storedToken,
      hasStoredRefreshToken: !!storedRefreshToken
    });
    
    if (storedUser && storedToken) {
      const userObj = JSON.parse(storedUser);
      console.log('Restored user from localStorage:', {
        userUuid: userObj.uuid,
        userEmail: userObj.email,
        userWarehouseUuid: userObj.warehouseUuidString || userObj.warehouseUuid,
        userWarehouseName: userObj.warehouseName
      });
      
      // Validate that the user UUID is still valid by fetching from backend
      const validateUserExists = async () => {
        try {
          console.log('Validating user UUID from localStorage:', userObj.uuid);
          const response = await fetch(`/api/users/${userObj.uuid}`, {
            headers: { Authorization: `Bearer ${storedToken}` },
          });
          
          if (response.status === 404) {
            console.log('User UUID not found in backend (404), clearing localStorage and redirecting to login');
            // User was deleted/recreated, clear localStorage and redirect to login
            localStorage.removeItem('dido_token');
            localStorage.removeItem('dido_refresh_token');
            localStorage.removeItem('dido_user');
            router.replace('/auth');
            return;
          }
          
          if (!response.ok) {
            console.log('Failed to validate user UUID:', response.status, response.statusText);
            // If we can't validate the user, clear localStorage and redirect to login
            localStorage.removeItem('dido_token');
            localStorage.removeItem('dido_refresh_token');
            localStorage.removeItem('dido_user');
            router.replace('/auth');
            return;
          }
          
          // User exists, proceed with normal flow
          console.log('User UUID validated successfully');
          setUser(userObj);
          setToken(storedToken);
          if (storedRefreshToken) {
            setRefreshToken(storedRefreshToken);
          }
          
          // Always trigger background fetch to ensure we have the latest warehouse info
          // This is especially important after database clearing when warehouse UUIDs may have changed
          console.log('Triggering background warehouse info fetch to ensure latest data from localStorage');
          setTimeout(() => {
            fetchAndPersistWarehouseInfo(userObj, storedToken);
          }, 0);
        } catch (error) {
          console.error('Error validating user UUID:', error);
          // If there's a network error, clear localStorage and redirect to login
          localStorage.removeItem('dido_token');
          localStorage.removeItem('dido_refresh_token');
          localStorage.removeItem('dido_user');
          router.replace('/auth');
        }
      };
      
      // Validate user exists before setting state
      validateUserExists();
    } else {
      console.log('No stored user/token found in localStorage');
    }
    
    // If we have no valid authentication and we're not on the auth page, redirect
    if (!storedToken && !window.location.pathname.includes('/auth')) {
      console.log('No valid authentication found, redirecting to login');
      router.replace('/auth');
    }
    
    setLoading(false);
  }, []); // Remove fetchAndPersistWarehouseInfo from dependencies to prevent infinite loop

  // Periodically validate session and refresh token if needed
  useEffect(() => {
    if (!token || !refreshToken) {
      console.log('No token or refresh token available, skipping periodic validation');
      return;
    }
    
    const interval = setInterval(async () => {
      try {
        console.log('Periodic token validation - checking token validity');
        // Try to validate current token
        const res = await fetch('/api/auth/validate', {
          headers: { Authorization: `Bearer ${token}` },
        });
        
        if (!res.ok) {
          console.log('Token validation failed, attempting refresh');
          // Token is invalid, try to refresh
          const refreshSuccess = await refreshAccessToken();
          if (!refreshSuccess) {
            console.log('Token refresh failed, logging out user');
            localStorage.removeItem('dido_token');
            localStorage.removeItem('dido_refresh_token');
            localStorage.removeItem('dido_user');
            router.replace("/auth");
          } else {
            console.log('Token refresh successful');
          }
        } else {
          console.log('Token validation successful');
        }
      } catch (error) {
        console.log('Network error during token validation, attempting refresh');
        // Network error, try to refresh token
        const refreshSuccess = await refreshAccessToken();
        if (!refreshSuccess) {
          console.log('Token refresh failed after network error, logging out user');
          localStorage.removeItem('dido_token');
          localStorage.removeItem('dido_refresh_token');
          localStorage.removeItem('dido_user');
          router.replace("/auth");
        } else {
          console.log('Token refresh successful after network error');
        }
      }
    }, 30 * 60 * 1000); // 30 minutes
    
    return () => clearInterval(interval);
  }, [token, refreshToken, refreshAccessToken, router]);

  const login = (user: User, token: string, refreshTokenValue: string) => {
    console.log('AuthContext login called with:', {
      userUuid: user.uuid,
      userEmail: user.email,
      userWarehouseUuid: user.warehouseUuidString || user.warehouseUuid,
      userWarehouseUuidString: user.warehouseUuidString,
      userWarehouseUuidLegacy: user.warehouseUuid,
      userWarehouseName: user.warehouseName,
      hasToken: !!token,
      hasRefreshToken: !!refreshTokenValue
    });
    
    setUser(user);
    setToken(token);
    setRefreshToken(refreshTokenValue);
    localStorage.setItem("dido_user", JSON.stringify(user));
    localStorage.setItem("dido_token", token);
    localStorage.setItem("dido_refresh_token", refreshTokenValue);
    
    // Always trigger background fetch to ensure we have the latest warehouse info
    // This is especially important after database clearing when warehouse UUIDs may have changed
    console.log('Triggering background warehouse info fetch to ensure latest data');
    setTimeout(() => {
      fetchAndPersistWarehouseInfo(user, token);
    }, 0);
  };

  const logout = async () => {
    console.log('AuthContext logout called - clearing session and redirecting to /auth');
    
    // Revoke tokens on backend if we have them
    if (token && refreshToken) {
      try {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            refreshToken,
            revokeAll: false, // Only revoke current session
          }),
        });
      } catch (error) {
        console.error('Error during logout:', error);
        // Continue with logout even if backend call fails
      }
    }
    
    setUser(null);
    setToken(null);
    setRefreshToken(null);
    localStorage.removeItem('dido_token');
    localStorage.removeItem('dido_refresh_token');
    localStorage.removeItem('dido_user');
    
    console.log('Session cleared, redirecting to /auth');
    router.replace("/auth");
  };

  const checkUserExists = useCallback(async (uuid: string): Promise<boolean> => {
    if (!token) return false;
    
    // Safety check: ensure UUID is valid
    if (!uuid || typeof uuid !== 'string' || uuid.trim() === '') {
      console.log('checkUserExists: invalid UUID provided:', uuid);
      return false;
    }
    
    try {
      const response = await fetch(`/api/users/${uuid}`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      
      if (response.status === 404) return false;
      if (!response.ok) throw new Error('Failed to check user existence');
      
      return true;
    } catch (error) {
      console.error('Error checking user existence:', error);
      return false;
    }
  }, [token]);

  const switchWarehouse = useCallback(async (warehouseUuid: string, warehouseName: string) => {
    if (!user?.uuid || !token) {
      throw new Error('User not authenticated');
    }
    
    // Safety check: ensure user UUID is valid
    if (typeof user.uuid !== 'string' || user.uuid.trim() === '') {
      throw new Error('Invalid user UUID');
    }

    try {
      // Update user's warehouse on backend
      const response = await fetch(`/api/users/${user.uuid}/warehouse`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ warehouseUuid }),
      });

      if (!response.ok) {
        throw new Error('Failed to update warehouse on backend');
      }

      // Update user context with new warehouse info
      const updatedUser: User = {
        ...user,
        warehouseUuid: warehouseUuid,
        warehouseName: warehouseName,
      };

      // Update localStorage
      localStorage.setItem("dido_user", JSON.stringify(updatedUser));
      
      // Update context state
      setUser(updatedUser);
      
      console.log('Warehouse switched successfully:', { warehouseUuid, warehouseName });
    } catch (error) {
      console.error('Error switching warehouse:', error);
      throw error;
    }
  }, [user, token]);

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        refreshToken,
        loading,
        login,
        logout,
        refreshAccessToken,
    
        fetchAndPersistWarehouseInfo,
        checkUserExists,
        switchWarehouse,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
