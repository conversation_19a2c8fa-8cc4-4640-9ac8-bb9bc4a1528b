import { ApiProperty } from "@nestjs/swagger";
import { IsUUID, IsString, IsOptional } from "class-validator";

export class CancelPurchaseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user cancelling the purchase",
  })
  @IsUUID("all")
  userUuid: string;

  @ApiProperty({
    example: "Cancelled due to supplier unavailability",
    description: "Reason for cancellation",
    required: false,
  })
  @IsOptional()
  @IsString()
  reason?: string;
} 