import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, OneToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { GoodsReceiptItem } from './goods-receipt-item.entity';

export enum GoodsReceiptStatus {
  PENDING = "pending",
  PARTIAL = "partial",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
}

@Entity('goods_receipts')
export class GoodsReceipt {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the goods receipt (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "GR-20250115-001",
    description: "Unique goods receipt number",
  })
  @Column({ unique: true })
  receiptNumber: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase this receipt is for",
  })
  @Column('uuid')
  @Index()
  purchaseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
  })
  @Column('uuid')
  @Index()
  supplierUuid: string;

  @ApiProperty({
    example: "Supplier Name",
    description: "Supplier name at time of receipt",
  })
  @Column()
  supplierName: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse where goods are received",
  })
  @Column('uuid')
  @Index()
  warehouseUuid: string;

  @ApiProperty({
    example: "Warehouse Name",
    description: "Warehouse name at time of receipt",
  })
  @Column()
  warehouseName: string;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Date when goods were received",
  })
  @Column()
  receiptDate: Date;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Expected receipt date",
  })
  @Column()
  expectedReceiptDate: Date;

  @ApiProperty({
    example: GoodsReceiptStatus.PENDING,
    description: "Current status of the goods receipt",
    enum: GoodsReceiptStatus,
  })
  @Column({
    type: 'enum',
    enum: GoodsReceiptStatus,
    default: GoodsReceiptStatus.PENDING,
  })
  status: GoodsReceiptStatus;

  @ApiProperty({
    example: "Goods received in good condition",
    description: "Notes about the receipt",
    required: false,
  })
  @Column({ nullable: true })
  notes?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the receipt",
  })
  @Column('uuid')
  @Index()
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the receipt",
  })
  @Column('uuid')
  @Index()
  updatedBy: string;

  @ApiProperty({
    example: false,
    description: "Whether the receipt has been soft deleted",
  })
  @Column({ default: false })
  isDeleted: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to GoodsReceiptItems
  @OneToMany(() => GoodsReceiptItem, receiptItem => receiptItem.goodsReceipt)
  receiptItems: GoodsReceiptItem[];

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 