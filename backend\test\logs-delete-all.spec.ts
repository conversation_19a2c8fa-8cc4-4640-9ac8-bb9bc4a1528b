import { Test, TestingModule } from '@nestjs/testing';
import { LogsService } from '../src/logs/logs.service';
import { LogsController } from '../src/logs/logs.controller';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Log } from '../src/logs/log.entity';

describe('Logs Delete All Endpoint', () => {
  let service: LogsService;
  let controller: LogsController;
  let mockLogsRepository: any;

  beforeEach(async () => {
    // Mock query builder
    const mockQueryBuilder = {
      delete: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue({ affected: 1500 }),
    };

    mockLogsRepository = {
      count: jest.fn(),
      delete: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue(mockQueryBuilder),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LogsService,
        {
          provide: getRepositoryToken(Log),
          useValue: mockLogsRepository,
        },
      ],
      controllers: [LogsController],
    }).compile();

    service = module.get<LogsService>(LogsService);
    controller = module.get<LogsController>(LogsController);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(controller).toBeDefined();
  });

  it('should delete all logs and return statistics', async () => {
    // Mock repository responses
    mockLogsRepository.count.mockResolvedValue(1500);

    const result = await service.deleteAll();

    expect(result).toEqual({
      deletedCount: 1500,
      message: 'Successfully deleted 1500 logs from database. This action is irreversible.'
    });

    expect(mockLogsRepository.count).toHaveBeenCalled();
    expect(mockLogsRepository.createQueryBuilder).toHaveBeenCalled();
  });

  it('should handle case when no logs exist', async () => {
    // Mock repository responses
    mockLogsRepository.count.mockResolvedValue(0);

    const result = await service.deleteAll();

    expect(result).toEqual({
      deletedCount: 0,
      message: 'No logs found to delete'
    });

    expect(mockLogsRepository.count).toHaveBeenCalled();
    expect(mockLogsRepository.delete).not.toHaveBeenCalled();
  });

  it('should handle controller endpoint', async () => {
    // Mock service response
    const mockServiceResponse = {
      deletedCount: 500,
      message: 'Successfully deleted 500 logs from database. This action is irreversible.'
    };

    jest.spyOn(service, 'deleteAll').mockResolvedValue(mockServiceResponse);

    const result = await controller.deleteAll();

    expect(result).toEqual(mockServiceResponse);
    expect(service.deleteAll).toHaveBeenCalled();
  });

  it('should include proper warnings in API documentation', () => {
    // This test verifies that the endpoint has proper warnings
    const deleteAllMethod = LogsController.prototype.deleteAll;
    expect(deleteAllMethod).toBeDefined();
    
    // The method should return a promise with deletion statistics
    expect(typeof deleteAllMethod).toBe('function');
  });
}); 