import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  ParseU<PERSON>DPipe,
  BadRequestException,
} from "@nestjs/common";
import { CreateSaleDto } from "./dto/create-sale.dto";
import { UpdateSaleDto } from "./dto/update-sale.dto";
import { CancelSaleDto } from "./dto/cancel-sale.dto";
import { UpdateSaleStatusDto } from "./dto/update-sale-status.dto";
import { DeleteSaleDto } from "./dto/delete-sale.dto";
import { AddProductsToSaleDto } from "./dto/add-products-to-sale.dto";
import {
  EMPTY_STRING_FILTER,
  EMPTY_UUID_FILTER,
  MIN_DATE_FILTER,
  MAX_DATE_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
} from "./sales.constants";
import { SalesService } from "./sales.service";
import { Sale, SaleStatus } from "./sale.entity";
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
import {
  SaleResponseDto,
  SalesListResponseDto,
} from "./dto/sales-response.dto";
import { Uuid7 } from "../utils/uuid7";
import { LogsService } from "../logs/logs.service";
import { LogsUtilityService } from "../logs/logs-utility.service";
import { In } from "typeorm";
// Utility function for debugging delay
const debugDelay = (ms: number = 3000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

@ApiTags("sales")
@Controller("sales")
export class SalesController {
  constructor(
    private readonly salesService: SalesService,
    private readonly logsService: LogsService,
    private readonly logsUtilityService: LogsUtilityService,
  ) {}

  /**
   * Utility method to get entity names for logging
   */
  private async getEntityNames(data: {
    customerUuid?: string;
    warehouseUuid?: string;
    userUuid?: string;
    productUuids?: string[];
  }) {
    const entityNames: any = {};
    
    try {
      // Get customer name
      if (data.customerUuid) {
        const customer = await this.salesService["customerRepository"].findOne({
          where: { id: data.customerUuid, isDeleted: false },
        });
        entityNames.customerName = customer?.name || "Unknown Customer";
      }

      // Get warehouse name
      if (data.warehouseUuid) {
        const warehouse = await this.salesService["warehouseRepository"]?.findOne({
          where: { id: data.warehouseUuid, isDeleted: false },
        });
        entityNames.warehouseName = warehouse?.name || "Unknown Warehouse";
      }

      // Get user name
      if (data.userUuid) {
        try {
          const user = await this.salesService["usersService"].findOne(data.userUuid);
          entityNames.userName = user?.name || "Unknown User";
        } catch (error) {
          entityNames.userName = "Unknown User";
        }
      }

      // Get product names
      if (data.productUuids && data.productUuids.length > 0) {
        const products = await this.salesService["productRepository"].find({
          where: { id: In(data.productUuids), isDeleted: false },
        });
        entityNames.productNames = products.reduce((acc, product) => {
          acc[product.id] = product.name;
          return acc;
        }, {} as Record<string, string>);
      }
    } catch (error) {
      // Error getting entity names
    }

    return entityNames;
  }



  @Post()
  @ApiOperation({ summary: "Create a new sale" })
  @ApiResponse({
    status: 201,
    description: "Sale created successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ 
    status: 400, 
    description: "Invalid input data - detailed validation errors for missing fields, invalid items, or empty sales" 
  })
  @ApiResponse({ 
    status: 404, 
    description: "User, warehouse, customer, or product not found" 
  })
  @ApiResponse({ 
    status: 400, 
    description: "Invalid payment amount - cannot overpay" 
  })
  @ApiResponse({ 
    status: 400, 
    description: "Invalid userUuid" 
  })
  async create(@Body() createSaleDto: CreateSaleDto): Promise<SaleResponseDto> {
    // Add 5-second delay for debugging
    await debugDelay();
    


    // Validate payment amount if provided
    if (createSaleDto.amountPaid !== undefined && createSaleDto.items && createSaleDto.items.length > 0) {
      // Calculate total amount
      const subtotal = createSaleDto.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
      const taxAmount = createSaleDto.useTax ? subtotal * (createSaleDto.taxRate || 0.1) : 0;
      const totalAmount = subtotal + taxAmount;
      
      if (createSaleDto.amountPaid > totalAmount) {
        throw new BadRequestException(
          `Payment amount (${createSaleDto.amountPaid}) cannot exceed total sale amount (${totalAmount}). Overpayment is not allowed.`
        );
      }
    }

    try {
      const sale = await this.salesService.create(
        createSaleDto.userUuid,
        createSaleDto.warehouseUuid,
        createSaleDto.customerUuid,
        createSaleDto.items,
        createSaleDto.useTax,
        createSaleDto.taxRate,
        createSaleDto.status,
        createSaleDto.paymentMethod,
        createSaleDto.amountPaid,
      );



      // Get entity names for logging
      const entityNames = await this.getEntityNames({
        customerUuid: createSaleDto.customerUuid,
        warehouseUuid: createSaleDto.warehouseUuid,
        userUuid: createSaleDto.userUuid,
        productUuids: createSaleDto.items?.map(item => item.productUuid) || [],
      });

      // Log the sale creation
      await this.logsService.create({
        userUuid: createSaleDto.userUuid,
        operation: "created",
        entityType: "sale",
        entity: `sale_${sale.uuid}`,
        description: `Sale created with ${createSaleDto.items?.length || 0} items`,
        data: {
          saleUuid: sale.uuid,
          invoiceNumber: sale.invoiceNumber,
          warehouseUuid: createSaleDto.warehouseUuid,
          warehouseName: entityNames.warehouseName,
          customerUuid: createSaleDto.customerUuid,
          customerName: entityNames.customerName,
          userName: entityNames.userName,
          itemsCount: createSaleDto.items?.length || 0,
          items: createSaleDto.items?.map(item => ({
            productUuid: item.productUuid,
            productName: entityNames.productNames?.[item.productUuid] || item.name,
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            lineTotal: item.totalPrice || item.quantity * item.unitPrice,
            taxAmount: 0, // CreateSaleDto doesn't have taxAmount per item
          })) || [],
          subtotal: sale.subtotal,
          useTax: sale.useTax,
          taxRate: sale.taxRate,
          taxAmount: sale.taxAmount,
          totalAmount: sale.totalAmount,
          amountPaid: sale.amountPaid,
          balanceDue: sale.balanceDue,
          status: sale.status,
          paymentMethod: sale.paymentMethod,
          invoiceDate: sale.invoiceDate,
          dueDate: sale.dueDate,
          createdAt: sale.createdAt,
        },
      });

      // Convert the sale document to SaleResponseDto
      return this.salesService.findOne(sale.uuid);
    } catch (error) {
      throw error;
    }
  }



  @Patch(":uuid/cancel")
  @ApiOperation({ summary: "Cancel a sale" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale cancelled successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  @ApiResponse({ status: 400, description: "Invalid userUuid" })
  async cancelSale(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() cancelSaleDto: CancelSaleDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    


    // Get original sale data before cancellation for delta logging
    let originalSale = null;
    try {
      originalSale = await this.salesService.findOne(uuid);
    } catch (error) {
      // Could not retrieve original sale for delta logging
    }

    const result = await this.salesService.cancelSale(uuid, cancelSaleDto.userUuid);

    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      userUuid: cancelSaleDto.userUuid,
    });

    // Log the sale cancellation with delta changes
    await this.logsUtilityService.logUpdate(
      cancelSaleDto.userUuid,
      `sale_${uuid}`,
      `Sale cancelled`,
      originalSale,
      result,
      {
        saleUuid: uuid,
        invoiceNumber: result.invoiceNumber,
        customerUuid: result.customerUuidString,
        customerName: result.customerName,
        userName: entityNames.userName,
        subtotal: result.subtotal,
        useTax: result.useTax,
        taxRate: result.taxRate,
        taxAmount: result.taxAmount,
        totalAmount: result.totalAmount,
        amountPaid: result.amountPaid,
        balanceDue: result.balanceDue,
        status: result.status,
        paymentMethod: result.paymentMethod,
        invoiceDate: result.invoiceDate,
        dueDate: result.dueDate,
        cancelledAt: new Date().toISOString(),
      }
    );
    
    return result;
  }

  @Patch(":uuid/status")
  @ApiOperation({ summary: "Update sale status with stock management" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale status updated successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  @ApiResponse({ status: 400, description: "Invalid status or userUuid" })
  async updateSaleStatus(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateSaleStatusDto: UpdateSaleStatusDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    


    // Get original sale data before status update for delta logging
    let originalSale = null;
    try {
      originalSale = await this.salesService.findOne(uuid);
    } catch (error) {
      // Could not retrieve original sale for delta logging
    }

    const result = await this.salesService.updateSaleStatus(uuid, updateSaleStatusDto.status, updateSaleStatusDto.userUuid);

    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      userUuid: updateSaleStatusDto.userUuid,
    });

    // Log the sale status update with delta changes
    await this.logsUtilityService.logUpdate(
      updateSaleStatusDto.userUuid,
      `sale_${uuid}`,
      `Sale status updated to ${updateSaleStatusDto.status}`,
      originalSale,
      result,
      {
        saleUuid: uuid,
        invoiceNumber: result.invoiceNumber,
        customerUuid: result.customerUuidString,
        customerName: result.customerName,
        userName: entityNames.userName,
        subtotal: result.subtotal,
        useTax: result.useTax,
        taxRate: result.taxRate,
        taxAmount: result.taxAmount,
        totalAmount: result.totalAmount,
        amountPaid: result.amountPaid,
        balanceDue: result.balanceDue,
        status: result.status,
        paymentMethod: result.paymentMethod,
        invoiceDate: result.invoiceDate,
        dueDate: result.dueDate,
        newStatus: updateSaleStatusDto.status,
        statusUpdatedAt: new Date().toISOString(),
      }
    );
    
    return result;
  }

  @Get()
  @ApiOperation({ summary: "Get all sales with pagination and filtering" })
  @ApiQuery({
    name: "customerUuid",
    required: false,
    type: String,
    description: "Filter by customer UUID (optional)",
  })
  @ApiQuery({
    name: "warehouseUuid",
    required: false,
    type: String,
    description: "Filter by warehouse UUID (optional)",
  })
  @ApiQuery({
    name: "status",
    required: false,
    type: String,
    description: "Filter by sale status (optional)",
  })
  @ApiQuery({
    name: "createdFrom",
    required: false,
    type: String,
    description: "Filter sales created after this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "createdTo",
    required: false,
    type: String,
    description: "Filter sales created before this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "invoiceNumber",
    required: false,
    type: String,
    description: "Filter by invoice number (optional, partial match)",
  })
  @ApiQuery({
    name: "paymentMethod",
    required: false,
    type: String,
    description: "Filter by payment method (optional)",
  })
  @ApiQuery({
    name: "startDate",
    required: false,
    type: String,
    description: "Filter sales with invoice date after this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "endDate",
    required: false,
    type: String,
    description: "Filter sales with invoice date before this date (optional, ISO 8601)",
  })
  @ApiQuery({
    name: "minAmount",
    required: false,
    type: Number,
    description: "Filter sales with total amount greater than or equal to this value (optional)",
  })
  @ApiQuery({
    name: "maxAmount",
    required: false,
    type: Number,
    description: "Filter sales with total amount less than or equal to this value (optional)",
  })
  @ApiQuery({
    name: "page",
    required: false,
    type: Number,
    description: "Page number (default: 1)",
  })
  @ApiQuery({
    name: "limit",
    required: false,
    type: Number,
    description: "Items per page (default: 10)",
  })
  @ApiResponse({
    status: 200,
    description: "Sales retrieved successfully",
    type: SalesListResponseDto,
  })
  async findAll(
    @Query("customerUuid") customerUuid?: string,
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("status") status?: string,
    @Query("createdFrom") createdFrom?: string,
    @Query("createdTo") createdTo?: string,
    @Query("invoiceNumber") invoiceNumber?: string,
    @Query("paymentMethod") paymentMethod?: string,
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string,
    @Query("minAmount") minAmount?: string,
    @Query("maxAmount") maxAmount?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ): Promise<SalesListResponseDto> {
    // Add 5-second delay for debugging
    await debugDelay();
    
    // Use constants for default values
    const customerUuidFilter = customerUuid ?? EMPTY_UUID_FILTER;
    const statusFilter = status ?? EMPTY_STRING_FILTER;

    // Use wide date range if not specified
    const createdFromDate = createdFrom
      ? new Date(createdFrom)
      : MIN_DATE_FILTER;
    const createdToDate = createdTo ? new Date(createdTo) : MAX_DATE_FILTER;

    // Parse date filters for invoice date range
    const startDateFilter = startDate ? new Date(startDate) : undefined;
    const endDateFilter = endDate ? new Date(endDate) : undefined;

    // Parse and validate amount filters
    const parsedMinAmount = minAmount ? parseFloat(minAmount) : undefined;
    const parsedMaxAmount = maxAmount ? parseFloat(maxAmount) : undefined;

    return this.salesService.findAll({
      customerUuid: customerUuidFilter,
      warehouseUuid,
      status: statusFilter,
      createdFrom: createdFromDate,
      createdTo: createdToDate,
      invoiceNumber,
      paymentMethod,
      startDate: startDateFilter,
      endDate: endDateFilter,
      minAmount: parsedMinAmount,
      maxAmount: parsedMaxAmount,
      page: page || 1,
      limit: limit || 10,
    });
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a sale by UUID" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale retrieved successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<SaleResponseDto> {
    // Add 5-second delay for debugging
    await debugDelay();
    
    return this.salesService.findOne(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update a sale" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sale updated successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  @ApiResponse({ status: 400, description: "Invalid payment amount - cannot overpay" })
  @ApiResponse({ status: 400, description: "Invalid userUuid" })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updateSaleDto: UpdateSaleDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    

    
    // Note: Payment amount validation is handled in the service after totals are recalculated
    // This ensures validation happens against the updated total amount, not the old one
    
    // Convert date strings to Date objects if provided
    const updateData: any = { ...updateSaleDto };
    if (updateData.invoiceDate) {
      updateData.invoiceDate = new Date(updateData.invoiceDate);
    }
    if (updateData.dueDate) {
      updateData.dueDate = new Date(updateData.dueDate);
    }
    // Removed paymentDate processing since it's redundant with updatedAt and excluded from logs
    


    try {
      // Get original sale data before update for delta logging
      let originalSale = null;
      try {
        originalSale = await this.salesService.findOne(uuid);
      } catch (error) {
        // Could not retrieve original sale for delta logging
      }

      const result = await this.salesService.update(uuid, updateData, updateSaleDto.userUuid);
      
      // Get entity names for logging
      const entityNames = await this.getEntityNames({
        customerUuid: updateSaleDto.customerUuid,
        userUuid: updateSaleDto.userUuid,
        productUuids: updateSaleDto.items?.map(item => item.productUuid) || [],
      });

      // Log the sale update with delta changes
      await this.logsUtilityService.logUpdate(
        updateSaleDto.userUuid,
        `sale_${uuid}`,
        `Sale updated`,
        originalSale,
        result,
        {
          saleUuid: uuid,
          invoiceNumber: result.invoiceNumber,
          customerUuid: updateSaleDto.customerUuid,
          customerName: entityNames.customerName,
          userName: entityNames.userName,
          subtotal: result.subtotal,
          useTax: result.useTax,
          taxRate: result.taxRate,
          taxAmount: result.taxAmount,
          totalAmount: result.totalAmount,
          amountPaid: result.amountPaid,
          balanceDue: result.balanceDue,
          status: result.status,
          paymentMethod: result.paymentMethod,
          invoiceDate: result.invoiceDate,
          dueDate: result.dueDate,
          updatedAt: new Date().toISOString(),
        }
      );
      
      return result;
    } catch (error) {
      throw error;
    }
  }

  @Post(":uuid/add-products")
  @ApiOperation({ summary: "Add products to an existing sale" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Products added to sale successfully",
    type: SaleResponseDto,
  })
  @ApiResponse({ status: 404, description: "Sale not found" })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  async addProductsToSale(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() addProductsDto: AddProductsToSaleDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    


    // Get original sale data before adding products for delta logging
    let originalSale = null;
    try {
      originalSale = await this.salesService.findOne(uuid);
    } catch (error) {
      // Could not retrieve original sale for delta logging
    }

    const result = await this.salesService.addProducts(uuid, addProductsDto.items);

    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      userUuid: addProductsDto.userUuid,
      productUuids: addProductsDto.items?.map(item => item.productUuid) || [],
    });

    // Log the products addition with delta changes
    await this.logsUtilityService.logUpdate(
      addProductsDto.userUuid,
      `sale_${uuid}`,
      `Added ${addProductsDto.items?.length || 0} products to sale`,
      originalSale,
      result,
      {
        saleUuid: uuid,
        invoiceNumber: result.invoiceNumber,
        customerUuid: result.customerUuidString,
        customerName: result.customerName,
        userName: entityNames.userName,
        itemsAdded: addProductsDto.items?.map(item => ({
          productUuid: item.productUuid,
          productName: entityNames.productNames?.[item.productUuid] || item.name,
          name: item.name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          lineTotal: item.lineTotal || item.quantity * item.unitPrice,
          taxAmount: item.taxAmount || 0,
        })) || [],
        itemsCount: addProductsDto.items?.length || 0,
        subtotal: result.subtotal,
        useTax: result.useTax,
        taxRate: result.taxRate,
        taxAmount: result.taxAmount,
        totalAmount: result.totalAmount,
        amountPaid: result.amountPaid,
        balanceDue: result.balanceDue,
        status: result.status,
        paymentMethod: result.paymentMethod,
        invoiceDate: result.invoiceDate,
        dueDate: result.dueDate,
        addedAt: new Date().toISOString(),
      }
    );
    
    return result;
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete a sale" })
  @ApiParam({
    name: "uuid",
    type: String,
    example: "uuid-v7-string",
    description: "Sale UUID",
  })
  @ApiResponse({ status: 200, description: "Sale deleted successfully" })
  @ApiResponse({ status: 404, description: "Sale not found" })
  async remove(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() deleteSaleDto: DeleteSaleDto,
  ) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    // Get sale details before deletion for logging
    let saleDetails = null;
    try {
      saleDetails = await this.salesService.findOne(uuid);
    } catch (error) {
      // Could not retrieve sale details for logging
    }
    
    const result = await this.salesService.remove(uuid);
    
    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      userUuid: deleteSaleDto.userUuid,
    });
    
    // Log the sale deletion with delta changes
    await this.logsUtilityService.logDeletion(
      deleteSaleDto.userUuid,
      `sale_${uuid}`,
      `Sale deleted`,
      saleDetails,
      {
        saleUuid: uuid,
        invoiceNumber: saleDetails?.invoiceNumber,
        customerUuid: saleDetails?.customerUuidString,
        customerName: saleDetails?.customerName,
        userName: entityNames.userName,
        subtotal: saleDetails?.subtotal,
        useTax: saleDetails?.useTax,
        taxRate: saleDetails?.taxRate,
        taxAmount: saleDetails?.taxAmount,
        totalAmount: saleDetails?.totalAmount,
        amountPaid: saleDetails?.amountPaid,
        balanceDue: saleDetails?.balanceDue,
        status: saleDetails?.status,
        paymentMethod: saleDetails?.paymentMethod,
        invoiceDate: saleDetails?.invoiceDate,
        dueDate: saleDetails?.dueDate,
        deletedAt: new Date().toISOString(),
      }
    );
    
    return result;
  }

  @Get("list-by-warehouse/:warehouseUuid")
  @ApiOperation({ summary: "Get all sales for a specific warehouse" })
  @ApiParam({
    name: "warehouseUuid",
    type: String,
    example: "uuid-v7-string",
    description: "Warehouse UUID",
  })
  @ApiResponse({
    status: 200,
    description: "Sales retrieved successfully",
    type: [SaleResponseDto],
  })
  async listByWarehouse(@Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string) {
    // Add 5-second delay for debugging
    await debugDelay();
    
    return this.salesService.findByWarehouse(warehouseUuid);
  }
}
