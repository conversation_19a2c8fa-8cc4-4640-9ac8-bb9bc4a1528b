{"name": "nestjs-backend-template", "version": "1.0.0", "description": "NestJS backend template with YugabyteDB, UUID users, Swagger", "main": "dist/main.js", "scripts": {"start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "fix-sales-data": "node scripts/fix-sales-data.js", "typeorm": "ts-node ./node_modules/typeorm/cli.js", "migration:generate": "node scripts/generate-migration.js", "migration:run": "python scripts/migration_manager_ysqlsh.py run", "migration:revert": "python scripts/migration_manager_ysqlsh.py revert", "migration:status": "python scripts/migration_manager_ysqlsh.py status", "migration:run-unsafe": "npm run typeorm migration:run -- -d src/data-source.ts", "migration:revert-unsafe": "npm run typeorm migration:revert -- -d src/data-source.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/typeorm": "^10.0.2", "@types/pg": "^8.15.4", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "google-auth-library": "^10.1.0", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.25", "uuid": "^9.0.1", "uuidv7": "^1.0.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "@types/uuid": "^9.0.8", "axios": "^1.7.2", "jest": "^29.5.0", "prettier": "^3.0.0", "supertest": "^7.1.4", "ts-jest": "^29.1.0", "ts-loader": "^9.4.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typeorm-ts-node-commonjs": "^0.3.20", "typescript": "^5.8.3"}, "license": "MIT"}