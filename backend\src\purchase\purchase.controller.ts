import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  ParseU<PERSON>DPipe,
  BadRequestException,
} from "@nestjs/common";
import { CreatePurchaseDto } from "./dto/create-purchase.dto";
import { UpdatePurchaseDto } from "./dto/update-purchase.dto";
import { CancelPurchaseDto } from "./dto/cancel-purchase.dto";
import { UpdatePurchaseStatusDto } from "./dto/update-purchase-status.dto";
import { DeletePurchaseDto } from "./dto/delete-purchase.dto";
import { AddProductsToPurchaseDto } from "./dto/add-products-to-purchase.dto";
import {
  EMPTY_STRING_FILTER,
  EMPTY_UUID_FILTER,
  MIN_DATE_FILTER,
  MAX_DATE_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
} from "./purchase.constants";
import { PurchaseService } from "./purchase.service";
import { Purchase, PurchaseStatus } from "./purchase.entity";
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
import {
  PurchaseResponseDto,
} from "./dto/purchase-response.dto";
import { Uuid7 } from "../utils/uuid7";
import { LogsService } from "../logs/logs.service";
import { LogsUtilityService } from "../logs/logs-utility.service";
import { In } from "typeorm";

@ApiTags("purchases")
@Controller("purchases")
export class PurchaseController {
  constructor(
    private readonly purchaseService: PurchaseService,
    private readonly logsService: LogsService,
    private readonly logsUtilityService: LogsUtilityService,
  ) {}

  /**
   * Utility method to get entity names for logging
   */
  private async getEntityNames(data: {
    supplierUuid?: string;
    warehouseUuid?: string;
    userUuid?: string;
    productUuids?: string[];
  }) {
    const entityNames: any = {};
    
    try {
      // Get supplier name
      if (data.supplierUuid) {
        const supplier = await this.purchaseService["supplierRepository"].findOne({
          where: { id: data.supplierUuid, isDeleted: false },
        });
        entityNames.supplierName = supplier?.name || "Unknown Supplier";
      }

      // Get warehouse name
      if (data.warehouseUuid) {
        const warehouse = await this.purchaseService["warehouseRepository"]?.findOne({
          where: { id: data.warehouseUuid, isDeleted: false },
        });
        entityNames.warehouseName = warehouse?.name || "Unknown Warehouse";
      }

      // Get user name
      if (data.userUuid) {
        try {
          const user = await this.purchaseService["usersService"].findOne(data.userUuid);
          entityNames.userName = user?.name || "Unknown User";
        } catch (error) {
          entityNames.userName = "Unknown User";
        }
      }

      // Get product names
      if (data.productUuids && data.productUuids.length > 0) {
        const products = await this.purchaseService["productRepository"].find({
          where: { id: In(data.productUuids), isDeleted: false },
        });
        entityNames.productNames = products.reduce((acc, product) => {
          acc[product.id] = product.name;
          return acc;
        }, {} as Record<string, string>);
      }
    } catch (error) {
      // Error getting entity names
    }

    return entityNames;
  }

  @Post()
  @ApiOperation({ summary: "Create a new purchase" })
  @ApiResponse({ status: 201, description: "Purchase created successfully", type: PurchaseResponseDto })
  async create(@Body() createPurchaseDto: CreatePurchaseDto): Promise<PurchaseResponseDto> {
    // Extract required fields for the service method
    const {
      createdBy,
      warehouseUuid,
      supplierUuid,
      purchaseItems,
      useTax,
      taxRate,
      status,
      paymentMethod,
    } = createPurchaseDto;

    const purchase = await this.purchaseService.create(
      createdBy,
      warehouseUuid,
      supplierUuid,
      purchaseItems,
      useTax,
      taxRate,
      status,
      paymentMethod,
    );

    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      supplierUuid: createPurchaseDto.supplierUuid,
      warehouseUuid: createPurchaseDto.warehouseUuid,
      userUuid: createPurchaseDto.createdBy,
      productUuids: createPurchaseDto.purchaseItems?.map(item => item.productUuid),
    });

    // Log the creation
    await this.logsService.create({
      userUuid: createPurchaseDto.createdBy,
      operation: "CREATED",
      entityType: "purchase",
      entity: purchase.uuid,
      description: `Purchase created by ${entityNames.userName}`,
      data: {
        purchaseNumber: purchase.purchaseNumber,
        supplierName: entityNames.supplierName,
        warehouseName: entityNames.warehouseName,
        totalAmount: purchase.totalAmount,
        itemCount: purchase.purchaseItems?.length || 0,
      },
    });

    return purchase;
  }

  @Patch(":uuid/cancel")
  @ApiOperation({ summary: "Cancel a purchase" })
  @ApiResponse({ status: 200, description: "Purchase cancelled successfully" })
  async cancelPurchase(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() cancelPurchaseDto: CancelPurchaseDto,
  ) {
    const purchase = await this.purchaseService.cancelPurchase(uuid, cancelPurchaseDto.userUuid);

    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      supplierUuid: purchase.supplierUuid,
      warehouseUuid: purchase.warehouseUuid,
      userUuid: cancelPurchaseDto.userUuid,
    });

    // Log the cancellation
    await this.logsService.create({
      userUuid: cancelPurchaseDto.userUuid,
      operation: "CANCELLED",
      entityType: "purchase",
      entity: purchase.uuid,
      description: `Purchase cancelled by ${entityNames.userName}`,
      data: {
        purchaseNumber: purchase.purchaseNumber,
        supplierName: entityNames.supplierName,
        warehouseName: entityNames.warehouseName,
        reason: cancelPurchaseDto.reason,
      },
    });

    return purchase;
  }

  @Patch(":uuid/status")
  @ApiOperation({ summary: "Update purchase status" })
  @ApiResponse({ status: 200, description: "Purchase status updated successfully" })
  async updatePurchaseStatus(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updatePurchaseStatusDto: UpdatePurchaseStatusDto,
  ) {
    const purchase = await this.purchaseService.updatePurchaseStatus(uuid, updatePurchaseStatusDto.newStatus, updatePurchaseStatusDto.userUuid);

    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      supplierUuid: purchase.supplierUuid,
      warehouseUuid: purchase.warehouseUuid,
      userUuid: updatePurchaseStatusDto.userUuid,
    });

    // Log the status update
    await this.logsService.create({
      userUuid: updatePurchaseStatusDto.userUuid,
      operation: "STATUS_UPDATED",
      entityType: "purchase",
      entity: purchase.uuid,
      description: `Purchase status updated by ${entityNames.userName}`,
      data: {
        purchaseNumber: purchase.purchaseNumber,
        supplierName: entityNames.supplierName,
        warehouseName: entityNames.warehouseName,
        oldStatus: updatePurchaseStatusDto.oldStatus,
        newStatus: updatePurchaseStatusDto.newStatus,
      },
    });

    return purchase;
  }

  @Get()
  @ApiOperation({ summary: "Get all purchases with filtering and pagination" })
  @ApiQuery({ name: "supplierUuid", required: false, description: "Filter by supplier UUID" })
  @ApiQuery({ name: "warehouseUuid", required: false, description: "Filter by warehouse UUID" })
  @ApiQuery({ name: "status", required: false, description: "Filter by purchase status" })
  @ApiQuery({ name: "createdFrom", required: false, description: "Filter by creation date from" })
  @ApiQuery({ name: "createdTo", required: false, description: "Filter by creation date to" })
  @ApiQuery({ name: "purchaseNumber", required: false, description: "Filter by purchase number" })
  @ApiQuery({ name: "paymentMethod", required: false, description: "Filter by payment method" })
  @ApiQuery({ name: "startDate", required: false, description: "Filter by purchase date from" })
  @ApiQuery({ name: "endDate", required: false, description: "Filter by purchase date to" })
  @ApiQuery({ name: "minAmount", required: false, description: "Filter by minimum total amount" })
  @ApiQuery({ name: "maxAmount", required: false, description: "Filter by maximum total amount" })
  @ApiQuery({ name: "page", required: false, description: "Page number for pagination" })
  @ApiQuery({ name: "limit", required: false, description: "Number of items per page" })
  async findAll(
    @Query("supplierUuid") supplierUuid?: string,
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("status") status?: string,
    @Query("createdFrom") createdFrom?: string,
    @Query("createdTo") createdTo?: string,
    @Query("purchaseNumber") purchaseNumber?: string,
    @Query("paymentMethod") paymentMethod?: string,
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string,
    @Query("minAmount") minAmount?: string,
    @Query("maxAmount") maxAmount?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ) {
    const filters = {
      supplierUuid: supplierUuid || EMPTY_UUID_FILTER,
      warehouseUuid: warehouseUuid || EMPTY_UUID_FILTER,
      status: status || EMPTY_STRING_FILTER,
      createdFrom: createdFrom ? new Date(createdFrom) : MIN_DATE_FILTER,
      createdTo: createdTo ? new Date(createdTo) : MAX_DATE_FILTER,
      purchaseOrderNumber: purchaseNumber || EMPTY_STRING_FILTER,
      paymentMethod: paymentMethod || EMPTY_STRING_FILTER,
      startDate: startDate ? new Date(startDate) : MIN_DATE_FILTER,
      endDate: endDate ? new Date(endDate) : MAX_DATE_FILTER,
      minAmount: minAmount ? parseFloat(minAmount) : MIN_NUMBER_FILTER,
      maxAmount: maxAmount ? parseFloat(maxAmount) : MAX_NUMBER_FILTER,
      page: page || 1,
      limit: limit || 10,
    };

    return await this.purchaseService.findAll(filters);
  }

  @Get(":uuid")
  @ApiOperation({ summary: "Get a purchase by UUID" })
  @ApiResponse({ status: 200, description: "Purchase found", type: PurchaseResponseDto })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<PurchaseResponseDto> {
    return await this.purchaseService.findOne(uuid);
  }

  @Patch(":uuid")
  @ApiOperation({ summary: "Update a purchase" })
  @ApiResponse({ status: 200, description: "Purchase updated successfully", type: PurchaseResponseDto })
  async update(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() updatePurchaseDto: UpdatePurchaseDto,
  ) {
    const purchase = await this.purchaseService.update(uuid, updatePurchaseDto, updatePurchaseDto.updatedBy);

    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      supplierUuid: updatePurchaseDto.supplierUuid,
      warehouseUuid: updatePurchaseDto.warehouseUuid,
      userUuid: updatePurchaseDto.updatedBy,
      productUuids: updatePurchaseDto.purchaseItems?.map(item => item.productUuid),
    });

    // Log the update
    await this.logsService.create({
      userUuid: updatePurchaseDto.updatedBy,
      operation: "UPDATED",
      entityType: "purchase",
      entity: purchase.uuid,
      description: `Purchase updated by ${entityNames.userName}`,
      data: {
        purchaseNumber: purchase.purchaseNumber,
        supplierName: entityNames.supplierName,
        warehouseName: entityNames.warehouseName,
        totalAmount: purchase.totalAmount,
        itemCount: purchase.purchaseItems?.length || 0,
      },
    });

    return purchase;
  }

  @Patch(":uuid/add-products")
  @ApiOperation({ summary: "Add products to a purchase" })
  @ApiResponse({ status: 200, description: "Products added to purchase successfully" })
  async addProductsToPurchase(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() addProductsDto: AddProductsToPurchaseDto,
  ) {
    const purchase = await this.purchaseService.addProducts(uuid, addProductsDto.purchaseItems);

    // Get entity names for logging
    const entityNames = await this.getEntityNames({
      supplierUuid: purchase.supplierUuid,
      warehouseUuid: purchase.warehouseUuid,
      userUuid: addProductsDto.userUuid,
      productUuids: addProductsDto.purchaseItems?.map(item => item.productUuid),
    });

    // Log the product addition
    await this.logsService.create({
      userUuid: addProductsDto.userUuid,
      operation: "PRODUCTS_ADDED",
      entityType: "purchase",
      entity: purchase.uuid,
      description: `Products added to purchase`,
      data: {
        purchaseNumber: purchase.purchaseNumber,
        supplierName: entityNames.supplierName,
        warehouseName: entityNames.warehouseName,
        addedItemCount: addProductsDto.purchaseItems?.length || 0,
        newTotalAmount: purchase.totalAmount,
      },
    });

    return purchase;
  }

  @Delete(":uuid")
  @ApiOperation({ summary: "Delete a purchase" })
  @ApiResponse({ status: 200, description: "Purchase deleted successfully" })
  async remove(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() deletePurchaseDto: DeletePurchaseDto,
  ) {
    const result = await this.purchaseService.remove(uuid);

    // Log the deletion
    await this.logsService.create({
      userUuid: deletePurchaseDto.userUuid,
      operation: "DELETED",
      entityType: "purchase",
      entity: uuid,
      description: `Purchase deleted`,
      data: {
        reason: "Soft delete",
      },
    });

    return result;
  }

  @Get("warehouse/:warehouseUuid")
  @ApiOperation({ summary: "Get all purchases for a specific warehouse" })
  @ApiResponse({ status: 200, description: "Purchases found" })
  async listByWarehouse(@Param("warehouseUuid", ParseUUIDPipe) warehouseUuid: string) {
    return await this.purchaseService.findByWarehouse(warehouseUuid);
  }
} 