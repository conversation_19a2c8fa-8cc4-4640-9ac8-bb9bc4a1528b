import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsNumber, IsUUID, IsEnum, IsDateString, IsArray, ValidateNested, Min } from "class-validator";
import { Type } from "class-transformer";
import { GoodsReceiptStatus } from "../goods-receipt.entity";

export class CreateGoodsReceiptItemDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase item this receipt item corresponds to",
  })
  @IsUUID("all")
  purchaseItemUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @IsUUID("all")
  productUuid: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of receipt",
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: 5,
    description: "Quantity ordered",
  })
  @IsNumber()
  @Min(0)
  quantityOrdered: number;

  @ApiProperty({
    example: 4,
    description: "Quantity received",
  })
  @IsNumber()
  @Min(0)
  quantityReceived: number;

  @ApiProperty({
    example: 1,
    description: "Quantity rejected/damaged",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  quantityRejected?: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of purchase",
  })
  @IsNumber()
  @Min(0)
  unitPrice: number;

  @ApiProperty({
    example: "Good condition",
    description: "Notes about the received items",
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    example: 0,
    description: "Order of the item in the receipt (0-based index)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  order?: number;
}

export class CreateGoodsReceiptDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase this receipt is for",
  })
  @IsUUID("all")
  purchaseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
  })
  @IsUUID("all")
  supplierUuid: string;

  @ApiProperty({
    example: "Supplier Name",
    description: "Supplier name at time of receipt",
  })
  @IsString()
  supplierName: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse where goods are received",
  })
  @IsUUID("all")
  warehouseUuid: string;

  @ApiProperty({
    example: "Warehouse Name",
    description: "Warehouse name at time of receipt",
  })
  @IsString()
  warehouseName: string;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Date when goods were received",
  })
  @IsDateString()
  receiptDate: string;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Expected receipt date",
  })
  @IsDateString()
  expectedReceiptDate: string;

  @ApiProperty({
    example: GoodsReceiptStatus.PENDING,
    description: "Current status of the goods receipt",
    enum: GoodsReceiptStatus,
  })
  @IsEnum(GoodsReceiptStatus)
  status: GoodsReceiptStatus;

  @ApiProperty({
    example: "Goods received in good condition",
    description: "Notes about the receipt",
    required: false,
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the receipt",
  })
  @IsUUID("all")
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the receipt",
  })
  @IsUUID("all")
  updatedBy: string;

  @ApiProperty({
    example: [{
      purchaseItemUuid: "uuid-v7-string",
      productUuid: "uuid-v7-string",
      name: "Product Name",
      quantityOrdered: 5,
      quantityReceived: 4,
      quantityRejected: 1,
      unitPrice: 25.99,
      notes: "Good condition",
      order: 0
    }],
    description: "Array of goods receipt items",
    type: [CreateGoodsReceiptItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateGoodsReceiptItemDto)
  receiptItems: CreateGoodsReceiptItemDto[];
} 