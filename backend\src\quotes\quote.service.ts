import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, In } from "typeorm";
import { Quote, QuoteStatus } from "./quote.entity";
import { QuoteItem } from "./quote-item.entity";
import { Customer } from "../customers/customer.entity";
import { Product } from "../products/product.entity";
import { Uuid7 } from "../utils/uuid7";

@Injectable()
export class QuoteService {
  constructor(
    @InjectRepository(Quote)
    private quoteRepository: Repository<Quote>,
    @InjectRepository(QuoteItem)
    private quoteItemRepository: Repository<QuoteItem>,
    @InjectRepository(Customer)
    private customerRepository: Repository<Customer>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
  ) {}

  /**
   * Create a new quote
   */
  async create(data: any): Promise<Quote> {
    const now = new Date();

    // Validate customer exists
    if (data.customerUuid) {
      const customer = await this.customerRepository.findOne({
        where: { id: data.customerUuid, isDeleted: false }
      });
      if (!customer) {
        throw new NotFoundException("Customer not found");
      }
    }

    // Generate quote number
    const timestamp = now.getTime();
    const quoteNumber = `QT-${timestamp}`;

    // Create quote entity
    const quote = this.quoteRepository.create({
      id: Quote.generateId(),
      quoteNumber,
      customerUuid: data.customerUuid,
      totalAmount: data.totalAmount || 0,
      status: data.status || QuoteStatus.DRAFT,
      quoteDate: data.quoteDate || now,
      expiryDate: data.expiryDate || new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      notes: data.notes,
      convertedToOrderUuid: data.convertedToOrderUuid || null,
      createdBy: data.createdBy,
      updatedBy: data.updatedBy,
      isDeleted: false,
    });

    const savedQuote = await this.quoteRepository.save(quote);

    // Create quote items if provided
    if (data.items && data.items.length > 0) {
      const validatedItems = await this.validateAndTransformItems(data.items);
      
      const quoteItems = validatedItems.map(item => 
        this.quoteItemRepository.create({
          id: QuoteItem.generateId(),
          quoteUuid: savedQuote.id,
          productUuid: item.productUuid,
          name: item.name,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          lineTotal: item.lineTotal,
        })
      );

      await this.quoteItemRepository.save(quoteItems);
    }

    return savedQuote;
  }

  /**
   * Find all quotes
   */
  async findAll(): Promise<Quote[]> {
    return this.quoteRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find quotes by warehouse (this would need business logic adjustment)
   */
  async findByWarehouse(warehouseUuid: string): Promise<Quote[]> {
    // Since quotes don't have warehouseUuid directly, we might need to filter differently
    // For now, return all quotes - this might need business logic adjustment
    return this.quoteRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find one quote by UUID
   */
  async findOne(uuid: string): Promise<Quote> {
    const quote = await this.quoteRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!quote) {
      throw new NotFoundException("Quote not found");
    }

    return quote;
  }

  /**
   * Update a quote
   */
  async update(uuid: string, data: Partial<Quote>): Promise<Quote> {
    const quote = await this.quoteRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!quote) {
      throw new NotFoundException("Quote not found");
    }

    // Update quote fields
    Object.assign(quote, data);
    quote.updatedAt = new Date();

    return this.quoteRepository.save(quote);
  }

  /**
   * Remove a quote (soft delete)
   */
  async remove(uuid: string): Promise<void> {
    const quote = await this.quoteRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!quote) {
      throw new NotFoundException("Quote not found");
    }

    quote.isDeleted = true;
    quote.updatedAt = new Date();
    await this.quoteRepository.save(quote);
  }

  /**
   * Add items to a quote
   */
  async addItemsToQuote(
    uuid: string,
    items: any[],
  ): Promise<Quote> {
    const quote = await this.quoteRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!quote) {
      throw new NotFoundException("Quote not found");
    }

    // Validate and transform items
    const validatedItems = await this.validateAndTransformItems(items);

    // Create new quote items
    const quoteItems = validatedItems.map(item => 
      this.quoteItemRepository.create({
        id: QuoteItem.generateId(),
        quoteUuid: quote.id,
        productUuid: item.productUuid,
        name: item.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        lineTotal: item.lineTotal,
      })
    );

    await this.quoteItemRepository.save(quoteItems);

    // Recalculate total amount
    const allItems = await this.quoteItemRepository.find({
      where: { quoteUuid: quote.id },
    });

    const totalAmount = this.roundToTwoDecimals(
      allItems.reduce((sum, item) => sum + item.lineTotal, 0)
    );

    quote.totalAmount = totalAmount;
    quote.updatedAt = new Date();
    await this.quoteRepository.save(quote);

    return quote;
  }

  /**
   * Mark quote as converted to order
   */
  async markAsConverted(uuid: string, orderUuid: string): Promise<Quote> {
    const quote = await this.quoteRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!quote) {
      throw new NotFoundException("Quote not found");
    }

    quote.status = QuoteStatus.CONVERTED;
    quote.convertedToOrderUuid = orderUuid;
    quote.updatedAt = new Date();
    
    return this.quoteRepository.save(quote);
  }

  /**
   * Get quote items
   */
  async getQuoteItems(quoteUuid: string): Promise<QuoteItem[]> {
    return this.quoteItemRepository.find({
      where: { quoteUuid },
    });
  }

  /**
   * Validate and transform items
   */
  private async validateAndTransformItems(items: any[]): Promise<any[]> {
    const productUuids = items.map(item => item.productUuid);
    const products = await this.productRepository.find({
      where: { id: In(productUuids), isDeleted: false },
    });

    const productMap = new Map(products.map(p => [p.id, p]));

    return items.map(item => {
      const product = productMap.get(item.productUuid);
      if (!product) {
        throw new BadRequestException(`Product with UUID ${item.productUuid} not found`);
      }

      const quantity = parseFloat(item.quantity);
      const unitPrice = parseFloat(item.unitPrice);
      const lineTotal = this.roundToTwoDecimals(quantity * unitPrice);

      return {
        productUuid: item.productUuid,
        name: product.name,
        quantity,
        unitPrice,
        lineTotal,
      };
    });
  }

  /**
   * Utility function to round numbers to 2 decimal places
   */
  private roundToTwoDecimals(value: number): number {
    return Math.round(value * 100) / 100;
  }
} 