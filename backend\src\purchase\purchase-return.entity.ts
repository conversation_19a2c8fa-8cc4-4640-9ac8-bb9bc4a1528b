import { Entity, PrimaryColumn, Column, CreateDateColumn, UpdateDateColumn, Index, OneToMany, JoinColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Uuid7 } from '../utils/uuid7';
import { PurchaseReturnItem } from './purchase-return-item.entity';

export enum PurchaseReturnStatus {
  PENDING = "pending",
  APPROVED = "approved",
  SHIPPED = "shipped",
  RECEIVED_BY_SUPPLIER = "received_by_supplier",
  REFUNDED = "refunded",
  CANCELLED = "cancelled",
}

@Entity('purchase_returns')
export class PurchaseReturn {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase return (primary key)",
  })
  @PrimaryColumn('uuid')
  id: string;

  @ApiProperty({
    example: "PR-20250115-001",
    description: "Unique purchase return number",
  })
  @Column({ unique: true })
  returnNumber: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase being returned",
  })
  @Column('uuid')
  @Index()
  purchaseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  supplierUuid?: string;

  @ApiProperty({
    example: "Supplier Name",
    description: "Supplier name at time of return",
    required: false,
  })
  @Column({ nullable: true })
  supplierName?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse where goods are being returned from",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  warehouseUuid?: string;

  @ApiProperty({
    example: "Warehouse Name",
    description: "Warehouse name at time of return",
    required: false,
  })
  @Column({ nullable: true })
  warehouseName?: string;

  @ApiProperty({
    example: "damaged_goods",
    description: "Primary reason for the return",
  })
  @Column()
  returnReason: string;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Date when return was initiated",
  })
  @Column()
  returnDate: Date;

  @ApiProperty({
    example: "2025-01-20T10:30:00.000Z",
    description: "Expected date when supplier will receive the return",
    required: false,
  })
  @Column({ nullable: true })
  expectedReturnDate?: Date;

  @ApiProperty({
    example: "Items were damaged during shipping and are not usable",
    description: "Detailed description of the return reason",
    required: false,
  })
  @Column({ nullable: true })
  returnDescription?: string;

  @ApiProperty({
    example: 51.98,
    description: "Total amount being returned",
  })
  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  totalReturnAmount: number;

  @ApiProperty({
    example: PurchaseReturnStatus.PENDING,
    description: "Current status of the return",
    enum: PurchaseReturnStatus,
  })
  @Column({
    type: 'enum',
    enum: PurchaseReturnStatus,
    default: PurchaseReturnStatus.PENDING,
  })
  status: PurchaseReturnStatus;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who created the return",
  })
  @Column('uuid')
  @Index()
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the return",
  })
  @Column('uuid')
  @Index()
  updatedBy: string;

  @ApiProperty({
    example: false,
    description: "Whether the return has been soft deleted",
  })
  @Column({ default: false })
  isDeleted: boolean;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Date when return was approved",
    required: false,
  })
  @Column({ nullable: true })
  approvedAt?: Date;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who approved the return",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  approvedBy?: string;

  @ApiProperty({
    example: "2025-01-16T10:30:00.000Z",
    description: "Date when return was shipped",
    required: false,
  })
  @Column({ nullable: true })
  shippedAt?: Date;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who shipped the return",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  shippedBy?: string;

  @ApiProperty({
    example: "2025-01-18T10:30:00.000Z",
    description: "Date when supplier received the return",
    required: false,
  })
  @Column({ nullable: true })
  receivedBySupplierAt?: Date;

  @ApiProperty({
    example: "2025-01-20T10:30:00.000Z",
    description: "Date when refund was processed",
    required: false,
  })
  @Column({ nullable: true })
  refundedAt?: Date;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who processed the refund",
    required: false,
  })
  @Column('uuid', { nullable: true })
  @Index()
  refundedBy?: string;

  @ApiProperty({
    example: "Tracking number for return shipment",
    description: "Tracking information for return shipment",
    required: false,
  })
  @Column({ nullable: true })
  trackingNumber?: string;

  @ApiProperty({
    example: "Return shipping method",
    description: "Method used to ship the return",
    required: false,
  })
  @Column({ nullable: true })
  shippingMethod?: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relationship to PurchaseReturnItems
  @OneToMany(() => PurchaseReturnItem, returnItem => returnItem.purchaseReturn)
  returnItems: PurchaseReturnItem[];

  // Helper method to generate UUID
  static generateId(): string {
    return new Uuid7().toString();
  }

  // Helper method to generate UUID
  static fromBinary(binary: any): string {
    if (!binary) return null;
    try {
      return new Uuid7(binary).toString();
    } catch {
      return null;
    }
  }
} 