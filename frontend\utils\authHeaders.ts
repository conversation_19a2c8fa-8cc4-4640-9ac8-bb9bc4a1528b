// Centralized utility for authentication headers
// JWT tokens are used for all authenticated endpoints

/**
 * Get authorization headers with <PERSON><PERSON><PERSON> token for all endpoints
 * @returns Headers object with Authorization Bearer token
 */
export function getAuthHeaders(): { Authorization: string } | {} {
  const token = localStorage.getItem('dido_token');
  return token ? { Authorization: `<PERSON><PERSON> ${token}` } : {};
}

/**
 * Get authorization headers with <PERSON><PERSON><PERSON> token for all endpoints
 * @returns Headers object with Authorization Bearer token and Content-Type
 */
export function getAuthHeadersWithContentType(): HeadersInit {
  const token = localStorage.getItem('dido_token');
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

/**
 * Clear all authentication tokens from localStorage
 */
export function clearAuthTokens(): void {
  console.log('Clearing authentication tokens');
  localStorage.removeItem('dido_token');
  localStorage.removeItem('dido_refresh_token');
  localStorage.removeItem('dido_user');
  
  // Dispatch a custom event to notify components that tokens were cleared
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('tokensCleared'));
  }
}

/**
 * Enhanced fetch wrapper for all endpoints
 * @param url - The URL to fetch
 * @param options - Fetch options
 * @returns Promise with the response
 */
export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  // Add auth headers for all endpoints if token exists
  if (!options.headers) {
    options.headers = getAuthHeadersWithContentType();
  } else if (!options.headers['Authorization']) {
    const authHeaders = getAuthHeadersWithContentType();
    options.headers = { ...options.headers, ...authHeaders };
  }

  try {
    const response = await fetch(url, options);
    
    // If we get a 401, clear tokens and redirect to login
    if (response.status === 401) {
      console.log('Received 401 Unauthorized, clearing tokens and redirecting to login');
      clearAuthTokens();
      
      // Redirect to login page only if not already on auth page
      if (typeof window !== 'undefined' && !window.location.pathname.includes('/auth')) {
        window.location.href = '/auth';
      }
    }
    
    return response;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
}

/**
 * Get authorization headers with JWT token for all endpoints
 * @returns Headers object with Authorization Bearer token
 */
export function getAxiosAuthHeaders(): { Authorization?: string } {
  const token = localStorage.getItem('dido_token');
  console.log('[getAxiosAuthHeaders] Token check:', {
    exists: !!token,
    length: token?.length,
    preview: token ? `${token.substring(0, 20)}...` : 'none'
  });
  const headers = token ? { Authorization: `Bearer ${token}` } : {};
  console.log('[getAxiosAuthHeaders] Returning headers:', headers);
  return headers;
}

/**
 * Setup global fetch interceptor for automatic 401 handling on all endpoints
 * This should be called once when the app initializes
 */
export function setupGlobalFetchInterceptor(): void {
  if (typeof window === 'undefined') return; // Only run on client side
  
  // Store the original fetch
  const originalFetch = window.fetch;
  
  // Override fetch to add auth headers for all endpoints
  window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
    const url = typeof input === 'string' ? input : input.toString();
    
    // Add auth headers for all endpoints if token exists
    const token = localStorage.getItem('dido_token');
    if (token) {
      const headers = new Headers(init?.headers);
      if (!headers.has('Authorization')) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      init = { ...init, headers };
    }
    
    try {
      const response = await originalFetch(input, init);
      
      // Handle 401 for all endpoints
      if (response.status === 401) {
        console.log('Global fetch interceptor: 401, clearing tokens');
        clearAuthTokens();
        
        // Redirect to login page only if not already on auth page
        if (!window.location.pathname.includes('/auth')) {
          window.location.href = '/auth';
        }
      }
      
      return response;
    } catch (error) {
      console.error('Global fetch interceptor error:', error);
      throw error;
    }
  };
}

/**
 * Setup axios interceptors for automatic token refresh on all endpoints
 * @param axiosInstance - The axios instance to configure
 * @param refreshAccessToken - Function to refresh the access token
 * @param logoutCallback - Optional callback for logout
 */
export function setupAxiosInterceptors(axiosInstance: any, refreshAccessToken: () => Promise<boolean>, logoutCallback?: () => void) {
  let isRefreshing = false;
  let failedQueue: Array<{ resolve: (value?: any) => void; reject: (reason?: any) => void }> = [];

  const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });
    failedQueue = [];
  };

  // Request interceptor - add auth headers for all endpoints
  axiosInstance.interceptors.request.use(
    (config: any) => {
      const token = localStorage.getItem('dido_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error: any) => {
      return Promise.reject(error);
    }
  );

  // Response interceptor - handle 401 for all endpoints
  axiosInstance.interceptors.response.use(
    (response: any) => {
      return response;
    },
    async (error: any) => {
      const originalRequest = error.config;
      const url = originalRequest.url || '';

      if (error.response?.status === 401 && !originalRequest._retry) {
        if (isRefreshing) {
          // If already refreshing, queue this request
          return new Promise((resolve, reject) => {
            failedQueue.push({ resolve, reject });
          }).then(() => {
            return axiosInstance(originalRequest);
          }).catch((err: any) => {
            return Promise.reject(err);
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          const refreshSuccess = await refreshAccessToken();
          if (refreshSuccess) {
            // Update the original request with new token
            const newToken = localStorage.getItem('dido_token');
            if (newToken) {
              originalRequest.headers['Authorization'] = `Bearer ${newToken}`;
            }
            processQueue(null, newToken);
            return axiosInstance(originalRequest);
          } else {
            processQueue(error, null);
            // Use logout callback if provided, otherwise fallback to window.location
            if (logoutCallback) {
              logoutCallback();
            } else {
              console.warn('No logout callback provided, using window.location fallback');
              window.location.href = '/auth';
            }
            return Promise.reject(error);
          }
        } catch (refreshError) {
          processQueue(refreshError, null);
          // Use logout callback if provided, otherwise fallback to window.location
          if (logoutCallback) {
            logoutCallback();
          } else {
            console.warn('No logout callback provided, using window.location fallback');
            window.location.href = '/auth';
          }
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }

      return Promise.reject(error);
    }
  );
} 