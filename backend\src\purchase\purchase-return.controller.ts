import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  ParseU<PERSON><PERSON>ipe,
  BadRequestException,
} from "@nestjs/common";
import { CreatePurchaseReturnDto, UpdatePurchaseReturnDto } from "./dto/purchase-return.dto";
import {
  EMPTY_STRING_FILTER,
  EMPTY_UUID_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
} from "./purchase.constants";
import { PurchaseReturnService } from "./purchase-return.service";
import { PurchaseReturnStatus } from "./purchase-return.entity";
import {
  ApiTags,
  ApiQuery,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from "@nestjs/swagger";
import {
  PurchaseReturnResponseDto,
} from "./dto/purchase-return.dto";
import { Uuid7 } from "../utils/uuid7";
import { LogsService } from "../logs/logs.service";
import { LogsUtilityService } from "../logs/logs-utility.service";

@ApiTags("purchase-returns")
@Controller("purchase-returns")
export class PurchaseReturnController {
  constructor(
    private readonly purchaseReturnService: PurchaseReturnService,
    private readonly logsService: LogsService,
    private readonly logsUtilityService: LogsUtilityService,
  ) {}

  /**
   * Create a new purchase return
   */
  @Post()
  @ApiOperation({ summary: "Create a new purchase return" })
  @ApiResponse({ status: 201, description: "Purchase return created successfully", type: PurchaseReturnResponseDto })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Purchase or user not found" })
  async create(@Body() createPurchaseReturnDto: CreatePurchaseReturnDto): Promise<PurchaseReturnResponseDto> {
    const {
      purchaseUuid,
      returnItems,
      returnReason,
      returnDate,
      expectedReturnDate,
      returnDescription,
      createdBy,
      updatedBy,
    } = createPurchaseReturnDto;

    const result = await this.purchaseReturnService.create(
      createdBy,
      purchaseUuid,
      returnItems,
      returnReason,
      new Date(returnDate),
      expectedReturnDate ? new Date(expectedReturnDate) : undefined,
      returnDescription,
    );

    // Log the creation
    await this.logsService.create({
      userUuid: createdBy,
      operation: "created",
      entityType: "purchase_return",
      entity: result.uuid,
      description: `Purchase return created for purchase ${purchaseUuid}`,
      data: {
        purchaseUuid,
        returnReason,
        totalAmount: result.totalReturnAmount,
        itemCount: returnItems.length,
      },
    });

    return result;
  }

  /**
   * Get all purchase returns with filtering
   */
  @Get()
  @ApiOperation({ summary: "Get all purchase returns with filtering" })
  @ApiQuery({ name: "purchaseUuid", required: false, description: "Filter by purchase UUID" })
  @ApiQuery({ name: "supplierUuid", required: false, description: "Filter by supplier UUID" })
  @ApiQuery({ name: "warehouseUuid", required: false, description: "Filter by warehouse UUID" })
  @ApiQuery({ name: "status", required: false, description: "Filter by return status" })
  @ApiQuery({ name: "returnReason", required: false, description: "Filter by return reason" })
  @ApiQuery({ name: "returnNumber", required: false, description: "Filter by return number" })
  @ApiQuery({ name: "createdFrom", required: false, description: "Filter by creation date from" })
  @ApiQuery({ name: "createdTo", required: false, description: "Filter by creation date to" })
  @ApiQuery({ name: "minAmount", required: false, description: "Filter by minimum return amount" })
  @ApiQuery({ name: "maxAmount", required: false, description: "Filter by maximum return amount" })
  @ApiQuery({ name: "page", required: false, description: "Page number", type: Number })
  @ApiQuery({ name: "limit", required: false, description: "Items per page", type: Number })
  @ApiResponse({ status: 200, description: "Returns list of purchase returns" })
  async findAll(
    @Query("purchaseUuid") purchaseUuid?: string,
    @Query("supplierUuid") supplierUuid?: string,
    @Query("warehouseUuid") warehouseUuid?: string,
    @Query("status") status?: string,
    @Query("returnReason") returnReason?: string,
    @Query("returnNumber") returnNumber?: string,
    @Query("createdFrom") createdFrom?: string,
    @Query("createdTo") createdTo?: string,
    @Query("minAmount") minAmount?: string,
    @Query("maxAmount") maxAmount?: string,
    @Query("page") page?: number,
    @Query("limit") limit?: number,
  ) {
    return this.purchaseReturnService.findAll({
      purchaseUuid,
      supplierUuid,
      warehouseUuid,
      status,
      returnReason,
      returnNumber,
      createdFrom: createdFrom ? new Date(createdFrom) : undefined,
      createdTo: createdTo ? new Date(createdTo) : undefined,
      minAmount: minAmount ? parseFloat(minAmount) : undefined,
      maxAmount: maxAmount ? parseFloat(maxAmount) : undefined,
      page,
      limit,
    });
  }

  /**
   * Get a single purchase return by ID
   */
  @Get(":uuid")
  @ApiOperation({ summary: "Get a single purchase return by ID" })
  @ApiParam({ name: "uuid", description: "Purchase return UUID" })
  @ApiResponse({ status: 200, description: "Returns the purchase return", type: PurchaseReturnResponseDto })
  @ApiResponse({ status: 404, description: "Purchase return not found" })
  async findOne(@Param("uuid", ParseUUIDPipe) uuid: string): Promise<PurchaseReturnResponseDto> {
    return this.purchaseReturnService.findOne(uuid);
  }

  /**
   * Approve a purchase return
   */
  @Patch(":uuid/approve")
  @ApiOperation({ summary: "Approve a purchase return" })
  @ApiParam({ name: "uuid", description: "Purchase return UUID" })
  @ApiResponse({ status: 200, description: "Purchase return approved successfully", type: PurchaseReturnResponseDto })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Purchase return not found" })
  async approveReturn(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string },
  ): Promise<PurchaseReturnResponseDto> {
    const result = await this.purchaseReturnService.approveReturn(uuid, body.userUuid);

    // Log the approval
    await this.logsService.create({
      userUuid: body.userUuid,
      operation: "approved",
      entityType: "purchase_return",
      entity: uuid,
      data: {
        previousStatus: "pending",
        newStatus: "approved",
      },
    });

    return result;
  }

  /**
   * Ship a purchase return
   */
  @Patch(":uuid/ship")
  @ApiOperation({ summary: "Mark a purchase return as shipped" })
  @ApiParam({ name: "uuid", description: "Purchase return UUID" })
  @ApiResponse({ status: 200, description: "Purchase return shipped successfully", type: PurchaseReturnResponseDto })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Purchase return not found" })
  async shipReturn(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string; trackingNumber?: string; shippingMethod?: string },
  ): Promise<PurchaseReturnResponseDto> {
    const result = await this.purchaseReturnService.shipReturn(
      uuid, 
      body.userUuid, 
      body.trackingNumber, 
      body.shippingMethod
    );

    // Log the shipping
    await this.logsService.create({
      userUuid: body.userUuid,
      operation: "shipped",
      entityType: "purchase_return",
      entity: uuid,
      description: "Purchase return shipped to supplier",
      data: {
        previousStatus: "approved",
        newStatus: "shipped",
        trackingNumber: body.trackingNumber,
        shippingMethod: body.shippingMethod,
      },
    });

    return result;
  }

  /**
   * Mark return as received by supplier
   */
  @Patch(":uuid/received-by-supplier")
  @ApiOperation({ summary: "Mark a purchase return as received by supplier" })
  @ApiParam({ name: "uuid", description: "Purchase return UUID" })
  @ApiResponse({ status: 200, description: "Purchase return marked as received", type: PurchaseReturnResponseDto })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Purchase return not found" })
  async markReceivedBySupplier(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string },
  ): Promise<PurchaseReturnResponseDto> {
    const result = await this.purchaseReturnService.markReceivedBySupplier(uuid, body.userUuid);

    // Log the receipt
    await this.logsService.create({
      userUuid: body.userUuid,
      operation: "received_by_supplier",
      entityType: "purchase_return",
      entity: uuid,
      description: "Purchase return received by supplier",
      data: {
        previousStatus: "shipped",
        newStatus: "received_by_supplier",
      },
    });

    return result;
  }

  /**
   * Process refund for return
   */
  @Patch(":uuid/refund")
  @ApiOperation({ summary: "Process refund for a purchase return" })
  @ApiParam({ name: "uuid", description: "Purchase return UUID" })
  @ApiResponse({ status: 200, description: "Refund processed successfully", type: PurchaseReturnResponseDto })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Purchase return not found" })
  async processRefund(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string },
  ): Promise<PurchaseReturnResponseDto> {
    const result = await this.purchaseReturnService.processRefund(uuid, body.userUuid);

    // Log the refund
    await this.logsService.create({
      userUuid: body.userUuid,
      operation: "refunded",
      entityType: "purchase_return",
      entity: uuid,
      description: "Purchase return refund processed",
      data: {
        previousStatus: "received_by_supplier",
        newStatus: "refunded",
        refundAmount: result.totalReturnAmount,
      },
    });

    return result;
  }

  /**
   * Cancel a purchase return
   */
  @Patch(":uuid/cancel")
  @ApiOperation({ summary: "Cancel a purchase return" })
  @ApiParam({ name: "uuid", description: "Purchase return UUID" })
  @ApiResponse({ status: 200, description: "Purchase return cancelled successfully", type: PurchaseReturnResponseDto })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 404, description: "Purchase return not found" })
  async cancelReturn(
    @Param("uuid", ParseUUIDPipe) uuid: string,
    @Body() body: { userUuid: string },
  ): Promise<PurchaseReturnResponseDto> {
    const result = await this.purchaseReturnService.cancelReturn(uuid, body.userUuid);

    // Log the cancellation
    await this.logsService.create({
      userUuid: body.userUuid,
      operation: "cancelled",
      entityType: "purchase_return",
      entity: uuid,
      description: "Purchase return cancelled",
      data: {
        newStatus: "cancelled",
      },
    });

    return result;
  }

  /**
   * Get return analytics
   */
  @Get("analytics/summary")
  @ApiOperation({ summary: "Get purchase return analytics" })
  @ApiQuery({ name: "startDate", required: false, description: "Start date for analytics" })
  @ApiQuery({ name: "endDate", required: false, description: "End date for analytics" })
  @ApiResponse({ status: 200, description: "Returns analytics data" })
  async getReturnAnalytics(
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string,
  ) {
    const dateRange = startDate && endDate ? {
      startDate: new Date(startDate),
      endDate: new Date(endDate),
    } : undefined;

    return this.purchaseReturnService.getReturnAnalytics(dateRange);
  }

  /**
   * Get returns by purchase
   */
  @Get("purchase/:purchaseUuid")
  @ApiOperation({ summary: "Get all returns for a specific purchase" })
  @ApiParam({ name: "purchaseUuid", description: "Purchase UUID" })
  @ApiResponse({ status: 200, description: "Returns list of purchase returns" })
  async getReturnsByPurchase(@Param("purchaseUuid", ParseUUIDPipe) purchaseUuid: string) {
    return this.purchaseReturnService.findAll({ purchaseUuid });
  }

  /**
   * Get returns by supplier
   */
  @Get("supplier/:supplierUuid")
  @ApiOperation({ summary: "Get all returns for a specific supplier" })
  @ApiParam({ name: "supplierUuid", description: "Supplier UUID" })
  @ApiResponse({ status: 200, description: "Returns list of purchase returns" })
  async getReturnsBySupplier(@Param("supplierUuid", ParseUUIDPipe) supplierUuid: string) {
    return this.purchaseReturnService.findAll({ supplierUuid });
  }
} 