import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Sale } from "./sale.entity";
import { SaleItem } from "./sale-item.entity";

import { SalesController } from "./sales.controller";
import { WarehousesModule } from "../warehouses/warehouses.module";
import { SalesService } from "./sales.service";
import { UsersModule } from "../users/users.module";
import { InventoryModule } from "../inventory/inventory.module";
import { CustomersModule } from "../customers/customers.module";
import { LogsModule } from "../logs/logs.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Sale,
      SaleItem,
    ]),
    WarehousesModule,
    UsersModule,
    InventoryModule,
    CustomersModule,
    LogsModule,
  ],
  controllers: [SalesController],
  providers: [SalesService],
  exports: [TypeOrmModule, SalesService],
})
export class SalesModule {}
