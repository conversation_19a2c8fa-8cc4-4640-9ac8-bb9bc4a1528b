import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Purchase } from "./purchase.entity";
import { PurchaseItem } from "./purchase-item.entity";
import { PurchaseReturn } from "./purchase-return.entity";
import { PurchaseReturnItem } from "./purchase-return-item.entity";
import { GoodsReceipt } from "./goods-receipt.entity";
import { GoodsReceiptItem } from "./goods-receipt-item.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { Supplier } from "../suppliers/supplier.entity";
import { Product } from "../products/product.entity";

import { PurchaseController } from "./purchase.controller";
import { PurchaseReturnController } from "./purchase-return.controller";
import { GoodsReceiptController } from "./goods-receipt.controller";
import { WarehousesModule } from "../warehouses/warehouses.module";
import { PurchaseService } from "./purchase.service";
import { PurchaseReturnService } from "./purchase-return.service";
import { GoodsReceiptService } from "./goods-receipt.service";
import { UsersModule } from "../users/users.module";
import { InventoryModule } from "../inventory/inventory.module";
import { SuppliersModule } from "../suppliers/suppliers.module";
import { LogsModule } from "../logs/logs.module";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Purchase,
      PurchaseItem,
      PurchaseReturn,
      PurchaseReturnItem,
      GoodsReceipt,
      GoodsReceiptItem,
      Warehouse,
      Supplier,
      Product,
    ]),
    WarehousesModule,
    UsersModule,
    InventoryModule,
    SuppliersModule,
    LogsModule,
  ],
  controllers: [
    PurchaseController, 
    PurchaseReturnController,
    GoodsReceiptController
  ],
  providers: [
    PurchaseService, 
    PurchaseReturnService,
    GoodsReceiptService
  ],
  exports: [
    TypeOrmModule, 
    PurchaseService, 
    PurchaseReturnService,
    GoodsReceiptService
  ],
})
export class PurchaseModule {} 