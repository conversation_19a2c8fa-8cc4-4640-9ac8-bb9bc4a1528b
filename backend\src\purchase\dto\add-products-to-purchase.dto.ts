import { ApiProperty } from "@nestjs/swagger";
import { IsUUID, IsArray, ValidateNested, IsString, IsNumber, IsOptional } from "class-validator";
import { Type } from "class-transformer";

export class AddProductToPurchaseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @IsUUID("all")
  productUuid: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of purchase",
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: 5,
    description: "Quantity to purchase",
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of purchase",
  })
  @IsNumber()
  unitPrice: number;

  @ApiProperty({
    example: 0,
    description: "Order of the item in the purchase (0-based index)",
    required: false,
  })
  @IsOptional()
  @IsNumber()
  order?: number;
}

export class AddProductsToPurchaseDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user adding products",
  })
  @IsUUID("all")
  userUuid: string;

  @ApiProperty({
    example: [{
      productUuid: "uuid-v7-string",
      name: "Product Name",
      quantity: 5,
      unitPrice: 25.99,
      order: 0
    }],
    description: "Array of products to add to the purchase",
    type: [AddProductToPurchaseDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AddProductToPurchaseDto)
  purchaseItems: AddProductToPurchaseDto[];
} 