import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, ILike, In } from "typeorm";
import { UsersService } from "../users/users.service";
import { GoodsReceipt, GoodsReceiptStatus } from "./goods-receipt.entity";
import { GoodsReceiptItem } from "./goods-receipt-item.entity";
import { Purchase, PurchaseStatus } from "./purchase.entity";
import { PurchaseItem } from "./purchase-item.entity";
import { Supplier } from "../suppliers/supplier.entity";
import { Product } from "../products/product.entity";
import { Warehouse } from "../warehouses/warehouse.entity";
import { 
  EMPTY_STRING_FILTER, 
  EMPTY_UUID_FILTER,
  MIN_NUMBER_FILTER,
  MAX_NUMBER_FILTER,
} from "./purchase.constants";
import {
  GoodsReceiptResponseDto,
  toGoodsReceiptResponseDto,
  toGoodsReceiptResponseDtoArray,
  GoodsReceiptListResponseDto,
} from "./dto/goods-receipt-response.dto";
import { InventoryService } from "../inventory/inventory.service.typeorm";

@Injectable()
export class GoodsReceiptService {
  constructor(
    @InjectRepository(GoodsReceipt)
    private goodsReceiptRepository: Repository<GoodsReceipt>,
    @InjectRepository(GoodsReceiptItem)
    private goodsReceiptItemRepository: Repository<GoodsReceiptItem>,
    @InjectRepository(Purchase)
    private purchaseRepository: Repository<Purchase>,
    @InjectRepository(PurchaseItem)
    private purchaseItemRepository: Repository<PurchaseItem>,
    @InjectRepository(Supplier)
    private supplierRepository: Repository<Supplier>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Warehouse)
    private warehouseRepository: Repository<Warehouse>,
    private usersService: UsersService,
    private inventoryService: InventoryService,
  ) {}

  /**
   * Utility function to round numbers to 2 decimal places
   */
  private roundToTwoDecimals(value: number): number {
    return Math.round(value * 100) / 100;
  }

  /**
   * Create a new Goods Receipt with validation
   */
  async create(
    purchaseUuid: string,
    supplierUuid: string,
    warehouseUuid: string,
    receiptItems: any[],
    receiptDate: Date,
    expectedReceiptDate: Date,
    status: GoodsReceiptStatus = GoodsReceiptStatus.PENDING,
    notes?: string,
    createdBy: string = "",
    updatedBy: string = "",
  ) {
    // Validate required fields
    if (!purchaseUuid || !supplierUuid || !warehouseUuid) {
      throw new BadRequestException(
        "purchaseUuid, supplierUuid, and warehouseUuid are required to create a goods receipt",
      );
    }

    if (!receiptItems || receiptItems.length === 0) {
      throw new BadRequestException("At least one receipt item is required");
    }

    // Check purchase exists
    const purchase = await this.purchaseRepository.findOne({
      where: { id: purchaseUuid, isDeleted: false }
    });
    if (!purchase) throw new NotFoundException("Purchase not found");

    // Check supplier exists
    const supplier = await this.supplierRepository.findOne({
      where: { id: supplierUuid, isDeleted: false }
    });
    if (!supplier) throw new NotFoundException("Supplier not found");

    // Check warehouse exists
    const warehouse = await this.warehouseRepository.findOne({
      where: { id: warehouseUuid, isDeleted: false }
    });
    if (!warehouse) throw new NotFoundException("Warehouse not found");

    // Validate receipt items
    const validatedItems = await this.validateReceiptItems(receiptItems, purchaseUuid);

    // Generate receipt number
    const receiptNumber = await this.generateReceiptNumber();

    // Create goods receipt
    const goodsReceipt = this.goodsReceiptRepository.create({
      id: GoodsReceipt.generateId(),
      receiptNumber,
      purchaseUuid,
      supplierUuid,
      supplierName: supplier.name,
      warehouseUuid,
      warehouseName: warehouse.name,
      receiptDate,
      expectedReceiptDate,
      status,
      notes,
      createdBy,
      updatedBy,
    });

    const savedReceipt = await this.goodsReceiptRepository.save(goodsReceipt);

    // Create receipt items
    const receiptItemEntities = validatedItems.map((item, index) => {
      return this.goodsReceiptItemRepository.create({
        id: GoodsReceiptItem.generateId(),
        goodsReceiptUuid: savedReceipt.id,
        purchaseItemUuid: item.purchaseItemUuid,
        productUuid: item.productUuid,
        name: item.name,
        quantityOrdered: item.quantityOrdered,
        quantityReceived: item.quantityReceived,
        quantityRejected: item.quantityRejected || 0,
        unitPrice: item.unitPrice,
        notes: item.notes,
        order: item.order || index,
      });
    });

    await this.goodsReceiptItemRepository.save(receiptItemEntities);

    // Update inventory if status is FULLY_RECEIVED or PARTIALLY_RECEIVED
    if (status === GoodsReceiptStatus.COMPLETED || status === GoodsReceiptStatus.PARTIAL) {
      await this.updateInventoryForReceipt(validatedItems, warehouseUuid, createdBy);
    }

    return this.findOne(savedReceipt.id);
  }

  /**
   * Create a partial goods receipt (for partial deliveries)
   */
  async createPartialReceipt(
    purchaseUuid: string,
    supplierUuid: string,
    warehouseUuid: string,
    receiptItems: any[],
    receiptDate: Date,
    partialDeliveryNotes?: string,
    createdBy: string = "",
  ) {
    // Validate that this is a partial receipt
    const purchase = await this.purchaseRepository.findOne({
      where: { id: purchaseUuid, isDeleted: false },
      relations: ['purchaseItems'],
    });
    
    if (!purchase) throw new NotFoundException("Purchase not found");

    // Check if this is actually a partial delivery
    const totalOrdered = purchase.purchaseItems.reduce((sum, item) => sum + item.quantity, 0);
    const totalReceived = receiptItems.reduce((sum, item) => sum + item.quantity, 0);

    if (totalReceived >= totalOrdered) {
      throw new BadRequestException("This appears to be a complete delivery, use create() instead");
    }

    // Create the partial receipt
    const receipt = await this.create(
      purchaseUuid,
      supplierUuid,
      warehouseUuid,
      receiptItems,
      receiptDate,
      purchase.expectedDeliveryDate,
      GoodsReceiptStatus.PARTIAL,
      partialDeliveryNotes,
      createdBy,
      createdBy,
    );

    // Update purchase status to PARTIALLY_RECEIVED
    await this.purchaseRepository.update(purchaseUuid, {
      status: PurchaseStatus.PARTIALLY_RECEIVED,
      updatedBy: createdBy,
    });

    return receipt;
  }

  /**
   * Complete a goods receipt (mark as fully received)
   */
  async completeReceipt(uuid: string, userUuid: string, completionNotes?: string): Promise<GoodsReceiptResponseDto> {
    const receipt = await this.goodsReceiptRepository.findOne({
      where: { id: uuid, isDeleted: false },
      relations: ['receiptItems'],
    });

    if (!receipt) {
      throw new NotFoundException("Goods receipt not found");
    }

    if (receipt.status === GoodsReceiptStatus.COMPLETED) {
      throw new BadRequestException("Goods receipt is already completed");
    }

    // Update status to completed
    receipt.status = GoodsReceiptStatus.COMPLETED;
    receipt.updatedBy = userUuid;
    if (completionNotes) {
      receipt.notes = receipt.notes ? `${receipt.notes}\n[COMPLETED] ${completionNotes}` : `[COMPLETED] ${completionNotes}`;
    }

    const updatedReceipt = await this.goodsReceiptRepository.save(receipt);

    // Update purchase status if all items are received
    await this.updatePurchaseStatusIfComplete(receipt.purchaseUuid, userUuid);

    return toGoodsReceiptResponseDto(updatedReceipt);
  }

  /**
   * Add quality control notes to a goods receipt
   */
  async addQualityControlNotes(
    uuid: string,
    userUuid: string,
    qualityNotes: string,
    qualityStatus: 'PASSED' | 'FAILED' | 'PARTIAL',
  ): Promise<GoodsReceiptResponseDto> {
    const receipt = await this.goodsReceiptRepository.findOne({
      where: { id: uuid, isDeleted: false },
    });

    if (!receipt) {
      throw new NotFoundException("Goods receipt not found");
    }

    // Add quality control notes
    const qualityControlEntry = `[QC-${qualityStatus}] ${qualityNotes} (by ${userUuid} on ${new Date().toISOString()})`;
    receipt.notes = receipt.notes ? `${receipt.notes}\n${qualityControlEntry}` : qualityControlEntry;
    receipt.updatedBy = userUuid;

    const updatedReceipt = await this.goodsReceiptRepository.save(receipt);
    return toGoodsReceiptResponseDto(updatedReceipt);
  }

  /**
   * Get goods receipt analytics for a warehouse
   */
  async getWarehouseReceiptAnalytics(warehouseUuid: string, dateRange?: { startDate: Date; endDate: Date }) {
    const query = this.goodsReceiptRepository
      .createQueryBuilder('receipt')
      .where('receipt.warehouseUuid = :warehouseUuid', { warehouseUuid })
      .andWhere('receipt.isDeleted = false');

    if (dateRange) {
      query.andWhere('receipt.createdAt BETWEEN :startDate AND :endDate', dateRange);
    }

    const receipts = await query.getMany();

    const analytics = {
      totalReceipts: receipts.length,
      totalItems: 0,
      averageReceiptValue: 0,
      statusBreakdown: {},
      supplierBreakdown: {},
      monthlyTrend: {},
      onTimeDeliveries: 0,
      lateDeliveries: 0,
      qualityIssues: 0,
    };

    receipts.forEach(receipt => {
      analytics.totalItems += receipt.receiptItems?.length || 0;
      
      // Status breakdown
      analytics.statusBreakdown[receipt.status] = (analytics.statusBreakdown[receipt.status] || 0) + 1;
      
      // Supplier breakdown
      analytics.supplierBreakdown[receipt.supplierName] = (analytics.supplierBreakdown[receipt.supplierName] || 0) + 1;
      
      // Monthly trend
      const month = receipt.createdAt.toISOString().substring(0, 7);
      analytics.monthlyTrend[month] = (analytics.monthlyTrend[month] || 0) + 1;
      
      // Delivery timing analysis
      if (receipt.receiptDate <= receipt.expectedReceiptDate) {
        analytics.onTimeDeliveries++;
      } else {
        analytics.lateDeliveries++;
      }
      
      // Quality issues (based on notes containing QC-FAILED)
      if (receipt.notes && receipt.notes.includes('QC-FAILED')) {
        analytics.qualityIssues++;
      }
    });

    analytics.averageReceiptValue = analytics.totalReceipts > 0 ? analytics.totalItems / analytics.totalReceipts : 0;

    return analytics;
  }

  /**
   * Get pending receipts for a warehouse
   */
  async getPendingReceipts(warehouseUuid?: string): Promise<GoodsReceiptResponseDto[]> {
    const query = this.goodsReceiptRepository
      .createQueryBuilder('receipt')
      .where('receipt.status = :status', { status: GoodsReceiptStatus.PENDING })
      .andWhere('receipt.isDeleted = false');

    if (warehouseUuid) {
      query.andWhere('receipt.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    query.orderBy('receipt.expectedReceiptDate', 'ASC');

    const receipts = await query.getMany();
    return toGoodsReceiptResponseDtoArray(receipts);
  }

  /**
   * Get overdue receipts
   */
  async getOverdueReceipts(warehouseUuid?: string): Promise<GoodsReceiptResponseDto[]> {
    const query = this.goodsReceiptRepository
      .createQueryBuilder('receipt')
      .where('receipt.expectedReceiptDate < :today', { today: new Date() })
      .andWhere('receipt.status IN (:...statuses)', { 
        statuses: [GoodsReceiptStatus.PENDING, GoodsReceiptStatus.PARTIAL] 
      })
      .andWhere('receipt.isDeleted = false');

    if (warehouseUuid) {
      query.andWhere('receipt.warehouseUuid = :warehouseUuid', { warehouseUuid });
    }

    query.orderBy('receipt.expectedReceiptDate', 'ASC');

    const receipts = await query.getMany();
    return toGoodsReceiptResponseDtoArray(receipts);
  }

  /**
   * Find all goods receipts with filtering and pagination
   */
  async findAll({
    purchaseUuid = EMPTY_UUID_FILTER,
    supplierUuid = EMPTY_UUID_FILTER,
    warehouseUuid = EMPTY_UUID_FILTER,
    status = EMPTY_STRING_FILTER,
    createdFrom,
    createdTo,
    page = 1,
    limit = 10,
    receiptNumber,
    startDate,
    endDate,
    minAmount = MIN_NUMBER_FILTER,
    maxAmount = MAX_NUMBER_FILTER,
  }: {
    purchaseUuid?: string;
    supplierUuid?: string;
    warehouseUuid?: string;
    status?: string;
    createdFrom?: Date;
    createdTo?: Date;
    page?: number;
    limit?: number;
    receiptNumber?: string;
    startDate?: Date;
    endDate?: Date;
    minAmount?: number;
    maxAmount?: number;
  } = {}): Promise<GoodsReceiptListResponseDto> {
    const queryBuilder = this.goodsReceiptRepository
      .createQueryBuilder("receipt")
      .where("receipt.isDeleted = :isDeleted", { isDeleted: false });

    // Apply filters
    if (purchaseUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere("receipt.purchaseUuid = :purchaseUuid", { purchaseUuid });
    }

    if (supplierUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere("receipt.supplierUuid = :supplierUuid", { supplierUuid });
    }

    if (warehouseUuid !== EMPTY_UUID_FILTER) {
      queryBuilder.andWhere("receipt.warehouseUuid = :warehouseUuid", { warehouseUuid });
    }

    if (status !== EMPTY_STRING_FILTER) {
      queryBuilder.andWhere("receipt.status = :status", { status });
    }

    if (receiptNumber) {
      queryBuilder.andWhere("receipt.receiptNumber ILIKE :receiptNumber", { 
        receiptNumber: `%${receiptNumber}%` 
      });
    }

    if (createdFrom) {
      queryBuilder.andWhere("receipt.createdAt >= :createdFrom", { createdFrom });
    }

    if (createdTo) {
      queryBuilder.andWhere("receipt.createdAt <= :createdTo", { createdTo });
    }

    if (startDate) {
      queryBuilder.andWhere("receipt.receiptDate >= :startDate", { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere("receipt.receiptDate <= :endDate", { endDate });
    }

    // Get total count
    const total = await queryBuilder.getCount();

    // Apply pagination
    const offset = (page - 1) * limit;
    queryBuilder
      .orderBy("receipt.createdAt", "DESC")
      .skip(offset)
      .take(limit);

    const receipts = await queryBuilder.getMany();

    // Enrich with items
    const enrichedReceipts = await this.enrichReceiptsData(receipts);

    return {
      receipts: enrichedReceipts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Find goods receipt by ID
   */
  async findOne(uuid: string): Promise<GoodsReceiptResponseDto> {
    const receipt = await this.goodsReceiptRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!receipt) {
      throw new NotFoundException("Goods receipt not found");
    }

    const items = await this.goodsReceiptItemRepository.find({
      where: { goodsReceiptUuid: uuid },
      order: { order: "ASC" }
    });

    return toGoodsReceiptResponseDto(receipt, items);
  }

  /**
   * Update goods receipt status
   */
  async updateStatus(
    uuid: string,
    newStatus: GoodsReceiptStatus,
    userUuid: string,
  ): Promise<GoodsReceiptResponseDto> {
    const receipt = await this.goodsReceiptRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!receipt) {
      throw new NotFoundException("Goods receipt not found");
    }

    // Update status
    receipt.status = newStatus;
    receipt.updatedBy = userUuid;
    receipt.updatedAt = new Date();

    await this.goodsReceiptRepository.save(receipt);

    // Update inventory if status changed to received
    if (newStatus === GoodsReceiptStatus.COMPLETED || newStatus === GoodsReceiptStatus.PARTIAL) {
      const items = await this.goodsReceiptItemRepository.find({
        where: { goodsReceiptUuid: uuid }
      });
      
      const receiptItems = items.map(item => ({
        productUuid: item.productUuid,
        quantityReceived: item.quantityReceived,
        quantityRejected: item.quantityRejected,
      }));

      await this.updateInventoryForReceipt(receiptItems, receipt.warehouseUuid, userUuid);
    }

    return this.findOne(uuid);
  }

  /**
   * Remove goods receipt (soft delete)
   */
  async remove(uuid: string): Promise<{ message: string }> {
    const receipt = await this.goodsReceiptRepository.findOne({
      where: { id: uuid, isDeleted: false }
    });

    if (!receipt) {
      throw new NotFoundException("Goods receipt not found");
    }

    receipt.isDeleted = true;
    await this.goodsReceiptRepository.save(receipt);

    return { message: "Goods receipt deleted successfully" };
  }

  /**
   * Find receipts by warehouse
   */
  async findByWarehouse(warehouseUuid: string): Promise<GoodsReceiptResponseDto[]> {
    const receipts = await this.goodsReceiptRepository.find({
      where: { warehouseUuid, isDeleted: false },
      order: { createdAt: "DESC" }
    });

    return this.enrichReceiptsData(receipts);
  }

  /**
   * Find receipts by purchase
   */
  async findByPurchase(purchaseUuid: string): Promise<GoodsReceiptResponseDto[]> {
    const receipts = await this.goodsReceiptRepository.find({
      where: { purchaseUuid, isDeleted: false },
      order: { createdAt: "DESC" }
    });

    return this.enrichReceiptsData(receipts);
  }

  /**
   * Private helper methods
   */
  private async generateReceiptNumber(): Promise<string> {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, "");
    
    const lastReceipt = await this.goodsReceiptRepository.findOne({
      where: {
        receiptNumber: ILike(`GR-${dateStr}-%`)
      },
      order: { receiptNumber: "DESC" }
    });

    let sequence = 1;
    if (lastReceipt) {
      const lastSequence = parseInt(lastReceipt.receiptNumber.split('-')[2]);
      sequence = lastSequence + 1;
    }

    return `GR-${dateStr}-${sequence.toString().padStart(3, '0')}`;
  }

  private async validateReceiptItems(items: any[], purchaseUuid: string): Promise<any[]> {
    const validatedItems = [];

    for (const item of items) {
      // Check purchase item exists
      const purchaseItem = await this.purchaseItemRepository.findOne({
        where: { id: item.purchaseItemUuid, purchaseUuid }
      });
      if (!purchaseItem) {
        throw new BadRequestException(`Purchase item ${item.purchaseItemUuid} not found`);
      }

      // Check product exists
      const product = await this.productRepository.findOne({
        where: { id: item.productUuid, isDeleted: false }
      });
      if (!product) {
        throw new BadRequestException(`Product ${item.productUuid} not found`);
      }

      // Validate quantities
      if (item.quantityReceived > item.quantityOrdered) {
        throw new BadRequestException(`Quantity received cannot exceed quantity ordered for product ${product.name}`);
      }

      if (item.quantityRejected && item.quantityRejected > item.quantityOrdered) {
        throw new BadRequestException(`Quantity rejected cannot exceed quantity ordered for product ${product.name}`);
      }

      validatedItems.push({
        ...item,
        name: product.name,
        unitPrice: purchaseItem.unitPrice,
      });
    }

    return validatedItems;
  }

  private async updateInventoryForReceipt(
    items: any[],
    warehouseUuid: string,
    userUuid: string,
  ): Promise<void> {
    // Find default storage for warehouse
    const defaultStorage = await this.findDefaultWarehouseStorage(warehouseUuid);

    for (const item of items) {
      const quantityToAdd = item.quantityReceived - (item.quantityRejected || 0);
      
      if (quantityToAdd > 0) {
        await this.inventoryService.createInventoryItem({
          productUuid: item.productUuid,
          storageUuid: defaultStorage,
          quantity: quantityToAdd,
        });
      }
    }
  }

  private async findDefaultWarehouseStorage(warehouseUuid: string): Promise<string> {
    // This would typically query the warehouse's default storage location
    // For now, we'll use a simple approach
    return warehouseUuid; // Simplified - in real implementation, you'd query storage locations
  }

  private async enrichReceiptsData(receipts: GoodsReceipt[]): Promise<GoodsReceiptResponseDto[]> {
    if (receipts.length === 0) return [];

    const receiptIds = receipts.map(r => r.id);
    const items = await this.goodsReceiptItemRepository.find({
      where: { goodsReceiptUuid: In(receiptIds) },
      order: { order: "ASC" }
    });

    const itemsMap = new Map<string, GoodsReceiptItem[]>();
    items.forEach(item => {
      if (!itemsMap.has(item.goodsReceiptUuid)) {
        itemsMap.set(item.goodsReceiptUuid, []);
      }
      itemsMap.get(item.goodsReceiptUuid)!.push(item);
    });

    return toGoodsReceiptResponseDtoArray(receipts, itemsMap);
  }

  /**
   * Update purchase status if all items are received
   */
  private async updatePurchaseStatusIfComplete(purchaseUuid: string, userUuid: string): Promise<void> {
    const purchase = await this.purchaseRepository.findOne({
      where: { id: purchaseUuid, isDeleted: false },
      relations: ['purchaseItems'],
    });

    if (!purchase) return;

    // Get all receipts for this purchase
    const receipts = await this.goodsReceiptRepository.find({
      where: { purchaseUuid, isDeleted: false },
      relations: ['receiptItems'],
    });

    // Calculate total received quantities
    const receivedQuantities = new Map<string, number>();
    receipts.forEach(receipt => {
      receipt.receiptItems.forEach(item => {
        const current = receivedQuantities.get(item.productUuid) || 0;
        receivedQuantities.set(item.productUuid, current + item.quantityReceived);
      });
    });

    // Check if all items are fully received
    const allReceived = purchase.purchaseItems.every(item => {
      const received = receivedQuantities.get(item.productUuid) || 0;
      return received >= item.quantity;
    });

    if (allReceived) {
      await this.purchaseRepository.update(purchaseUuid, {
        status: PurchaseStatus.RECEIVED,
        actualDeliveryDate: new Date(),
        updatedBy: userUuid,
      });
    }
  }
} 