import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsNumber, IsUUID, IsEnum, IsDateString, IsArray, ValidateNested, <PERSON>, <PERSON> } from "class-validator";
import { Type } from "class-transformer";
import { RETURN_REASONS } from "../purchase.constants";

export class PurchaseReturnItemDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase item being returned",
  })
  @IsUUID("all")
  purchaseItemUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the product",
  })
  @IsUUID("all")
  productUuid: string;

  @ApiProperty({
    example: "Product Name",
    description: "Product name at time of return",
  })
  @IsString()
  name: string;

  @ApiProperty({
    example: 2,
    description: "Quantity being returned",
  })
  @IsNumber()
  @Min(0.01, { message: "Quantity must be greater than 0" })
  quantity: number;

  @ApiProperty({
    example: 25.99,
    description: "Unit price at time of purchase (for refund calculation)",
  })
  @IsNumber()
  @Min(0, { message: "Unit price must be at least 0" })
  unitPrice: number;

  @ApiProperty({
    example: "Damaged during shipping",
    description: "Reason for returning this specific item",
    required: false,
  })
  @IsOptional()
  @IsString()
  itemReturnReason?: string;
}

export class CreatePurchaseReturnDto {
  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the purchase being returned",
  })
  @IsUUID("all")
  purchaseUuid: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the supplier",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  supplierUuid?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the warehouse where goods are being returned from",
    required: false,
  })
  @IsOptional()
  @IsUUID("all")
  warehouseUuid?: string;

  @ApiProperty({
    example: RETURN_REASONS.DAMAGED_GOODS,
    description: "Primary reason for the return",
    enum: RETURN_REASONS,
  })
  @IsEnum(RETURN_REASONS)
  returnReason: string;

  @ApiProperty({
    example: "2025-01-15T10:30:00.000Z",
    description: "Date when return was initiated",
  })
  @IsDateString()
  returnDate: string;

  @ApiProperty({
    example: "2025-01-20T10:30:00.000Z",
    description: "Expected date when supplier will receive the return",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  expectedReturnDate?: string;

  @ApiProperty({
    example: "Items were damaged during shipping and are not usable",
    description: "Detailed description of the return reason",
    required: false,
  })
  @IsOptional()
  @IsString()
  returnDescription?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who initiated the return",
  })
  @IsUUID("all")
  createdBy: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the return",
  })
  @IsUUID("all")
  updatedBy: string;

  @ApiProperty({
    example: [{
      purchaseItemUuid: "uuid-v7-string",
      productUuid: "uuid-v7-string",
      name: "Product Name",
      quantity: 2,
      unitPrice: 25.99,
      itemReturnReason: "Damaged during shipping"
    }],
    description: "Array of items being returned",
    type: [PurchaseReturnItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PurchaseReturnItemDto)
  returnItems: PurchaseReturnItemDto[];
}

export class UpdatePurchaseReturnDto {
  @ApiProperty({
    example: "2025-01-20T10:30:00.000Z",
    description: "Expected date when supplier will receive the return",
    required: false,
  })
  @IsOptional()
  @IsDateString()
  expectedReturnDate?: string;

  @ApiProperty({
    example: "Items were damaged during shipping and are not usable",
    description: "Detailed description of the return reason",
    required: false,
  })
  @IsOptional()
  @IsString()
  returnDescription?: string;

  @ApiProperty({
    example: "uuid-v7-string",
    description: "The UUID of the user who last updated the return",
  })
  @IsUUID("all")
  updatedBy: string;
}

export class PurchaseReturnResponseDto {
  @ApiProperty({ example: "uuid-v7-string" })
  uuid: string;

  @ApiProperty({ example: "uuid-v7-string" })
  purchaseUuid: string;

  @ApiProperty({ example: "PR-20250115-001" })
  returnNumber: string;

  @ApiProperty({ example: "uuid-v7-string", required: false })
  supplierUuid?: string;

  @ApiProperty({ example: "Supplier Name", required: false })
  supplierName?: string;

  @ApiProperty({ example: "uuid-v7-string", required: false })
  warehouseUuid?: string;

  @ApiProperty({ example: "Warehouse Name", required: false })
  warehouseName?: string;

  @ApiProperty({ example: RETURN_REASONS.DAMAGED_GOODS, enum: RETURN_REASONS })
  returnReason: string;

  @ApiProperty({ example: "2025-01-15T10:30:00.000Z" })
  returnDate: string;

  @ApiProperty({ example: "2025-01-20T10:30:00.000Z", required: false })
  expectedReturnDate?: string;

  @ApiProperty({ example: "Items were damaged during shipping and are not usable", required: false })
  returnDescription?: string;

  @ApiProperty({ example: 51.98 })
  totalReturnAmount: number;

  @ApiProperty({ example: "pending" })
  status: string;

  @ApiProperty({ example: "uuid-v7-string" })
  createdBy: string;

  @ApiProperty({ example: "uuid-v7-string" })
  updatedBy: string;

  @ApiProperty({ example: "2025-01-15T10:30:00.000Z" })
  createdAt: string;

  @ApiProperty({ example: "2025-01-15T10:30:00.000Z" })
  updatedAt: string;

  @ApiProperty({ type: [PurchaseReturnItemDto] })
  returnItems: PurchaseReturnItemDto[];
}

export function toPurchaseReturnItemResponseDto(entity: any): PurchaseReturnItemDto {
  return {
    purchaseItemUuid: entity.purchaseItemUuid,
    productUuid: entity.productUuid,
    name: entity.name,
    quantity: entity.quantity,
    unitPrice: entity.unitPrice,
    itemReturnReason: entity.itemReturnReason,
  };
}

export function toPurchaseReturnResponseDto(entity: any): PurchaseReturnResponseDto {
  return {
    uuid: entity.id,
    purchaseUuid: entity.purchaseUuid,
    returnNumber: entity.returnNumber,
    supplierUuid: entity.supplierUuid,
    supplierName: entity.supplierName,
    warehouseUuid: entity.warehouseUuid,
    warehouseName: entity.warehouseName,
    returnReason: entity.returnReason,
    returnDate: entity.returnDate?.toISOString(),
    expectedReturnDate: entity.expectedReturnDate?.toISOString(),
    returnDescription: entity.returnDescription,
    totalReturnAmount: entity.totalReturnAmount,
    status: entity.status,
    createdBy: entity.createdBy,
    updatedBy: entity.updatedBy,
    createdAt: entity.createdAt?.toISOString(),
    updatedAt: entity.updatedAt?.toISOString(),
    returnItems: entity.returnItems?.map(toPurchaseReturnItemResponseDto) || [],
  };
}

export function toPurchaseReturnResponseDtoArray(entities: any[]): PurchaseReturnResponseDto[] {
  return entities.map(toPurchaseReturnResponseDto);
} 