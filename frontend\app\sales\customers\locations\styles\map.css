/* Map styling for customer locations */
.leaflet-container {
  height: 100% !important;
  width: 100% !important;
  border-radius: 0.5rem;
  min-height: 300px;
}

.leaflet-popup-content-wrapper {
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.leaflet-popup-content {
  margin: 0;
  padding: 0;
}

.leaflet-popup-close-button {
  color: #6B7280;
  font-size: 18px;
  font-weight: bold;
  padding: 4px 8px;
}

.leaflet-popup-close-button:hover {
  color: #374151;
}

.custom-marker {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.custom-marker:hover {
  transform: scale(1.1);
}

/* Ensure map controls are visible */
.leaflet-control-zoom {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.leaflet-control-zoom a {
  border-radius: 0.5rem;
  color: #374151;
  font-weight: bold;
}

.leaflet-control-zoom a:hover {
  background-color: #F3F4F6;
}

.leaflet-control-attribution {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0.25rem;
  font-size: 11px;
} 